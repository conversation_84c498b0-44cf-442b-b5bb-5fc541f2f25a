import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Lock, Unlock, RefreshCw, ShoppingCart } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { getPlaybackToken, checkEventAccess } from '@/services/event-service';

interface PlaybackData {
  requiresAuthorization: boolean;
  streamUrl?: string;
  embedUrl?: string;
  authorizedStreamUrl?: string;
  authorizedEmbedUrl?: string;
  token?: string;
  expiresInMinutes?: number;
  expiresAt?: string;
  message?: string;
  eventInfo?: {
    id: string;
    title: string;
    price: number;
    currency: string;
  };
  channelInfo?: {
    id: string;
    name: string;
    channelArn: string;
  };
}

interface AuthorizedVideoPlayerProps {
  eventId: string;
  className?: string;
  autoplay?: boolean;
  onError?: (error: string) => void;
  onTokenRefresh?: (newToken: string) => void;
  onPurchaseRequired?: () => void;
  // Event information for purchase UI
  eventThumbnail?: string;
  eventTitle?: string;
  eventPrice?: number;
  eventCurrency?: string;
}

export const AuthorizedVideoPlayer: React.FC<AuthorizedVideoPlayerProps> = ({
  eventId,
  className = '',
  autoplay = false,
  onError,
  onTokenRefresh,
  onPurchaseRequired,
  eventThumbnail,
  eventTitle,
  eventPrice,
  eventCurrency
}) => {
  const [playbackData, setPlaybackData] = useState<PlaybackData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tokenExpiring, setTokenExpiring] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [checkingAccess, setCheckingAccess] = useState(true);

  const iframeRef = useRef<HTMLIFrameElement>(null);
  const refreshTimerRef = useRef<NodeJS.Timeout | null>(null);
  const expirationWarningRef = useRef<NodeJS.Timeout | null>(null);

  const { toast } = useToast();

  useEffect(() => {
    if (eventId) {
      checkAccess();
    }

    return () => {
      // Cleanup timers
      if (refreshTimerRef.current) {
        clearTimeout(refreshTimerRef.current);
      }
      if (expirationWarningRef.current) {
        clearTimeout(expirationWarningRef.current);
      }
    };
  }, [eventId]);

  const checkAccess = async () => {
    try {
      setCheckingAccess(true);
      setError(null);

      const accessResult = await checkEventAccess(eventId);
      setHasAccess(accessResult);

      // Only fetch playback token if user has access
      if (accessResult) {
        await fetchPlaybackToken();
      } else {
        setLoading(false);
        setCheckingAccess(false);
      }
    } catch (err: any) {
      console.error('Error checking event access:', err);
      setHasAccess(false);
      setLoading(false);
      setCheckingAccess(false);

      if (onError) {
        onError('Failed to check event access');
      }
    }
  };

  const fetchPlaybackToken = async (showRefreshingState = false) => {
    try {
      if (showRefreshingState) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      setError(null);

      const data: PlaybackData = await getPlaybackToken(eventId);
      setPlaybackData(data);

      // Schedule token refresh and expiration warning for paid content
      if (data.requiresAuthorization && data.expiresAt) {
        scheduleTokenRefresh(data.expiresAt);
        scheduleExpirationWarning(data.expiresAt);

        if (onTokenRefresh && data.token) {
          onTokenRefresh(data.token);
        }
      }

      if (showRefreshingState) {
        toast({
          title: "Token Refreshed",
          description: "Your access has been renewed successfully.",
        });
      }

    } catch (err: any) {
      let errorMessage = 'Unknown error occurred';

      if (err.response?.status === 403) {
        errorMessage = 'You do not have access to this event. Please purchase it first.';
      } else if (err.response?.status === 401) {
        errorMessage = 'Please log in to access this content.';
      } else if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
      if (onError) {
        onError(errorMessage);
      }

      toast({
        title: "Playback Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
      setCheckingAccess(false);
    }
  };

  const scheduleTokenRefresh = (expiresAt: string) => {
    const expirationTime = new Date(expiresAt).getTime();
    const refreshTime = expirationTime - Date.now() - (5 * 60 * 1000); // 5 minutes before expiry

    if (refreshTime > 0) {
      refreshTimerRef.current = setTimeout(() => {
        fetchPlaybackToken(true);
      }, refreshTime);
    }
  };

  const scheduleExpirationWarning = (expiresAt: string) => {
    const expirationTime = new Date(expiresAt).getTime();
    const warningTime = expirationTime - Date.now() - (10 * 60 * 1000); // 10 minutes before expiry

    if (warningTime > 0) {
      expirationWarningRef.current = setTimeout(() => {
        setTokenExpiring(true);
        toast({
          title: "Access Expiring Soon",
          description: "Your access to this stream will expire in 10 minutes.",
          variant: "destructive",
        });
      }, warningTime);
    }
  };

  const manualRefresh = () => {
    fetchPlaybackToken(true);
    setTokenExpiring(false);
  };

  const refreshAccess = () => {
    checkAccess();
  };

  const getStreamUrl = () => {
    if (!playbackData) return null;

    // Prioritize authorized URLs if available
    if (playbackData.requiresAuthorization) {
      return playbackData.authorizedEmbedUrl || playbackData.authorizedStreamUrl;
    }

    // Fall back to regular URLs for free content
    return playbackData.embedUrl || playbackData.streamUrl;
  };

  const formatExpirationTime = (expiresAt: string) => {
    return new Date(expiresAt).toLocaleString();
  };

  if (checkingAccess || loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-8">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>{checkingAccess ? 'Checking access...' : 'Loading video player...'}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show purchase required UI if user doesn't have access
  if (hasAccess === false) {
    return (
      <Card className={className}>
        <CardContent className="p-0">
          {/* Event Thumbnail */}
          <div className="relative w-full" style={{ paddingBottom: '56.25%' }}>
            <div className="absolute top-0 left-0 w-full h-full bg-gray-900 rounded-t-lg overflow-hidden">
              {eventThumbnail ? (
                <img
                  src={eventThumbnail}
                  alt={eventTitle || 'Event thumbnail'}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/placeholder.svg';
                  }}
                />
              ) : (
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-500">No thumbnail available</span>
                </div>
              )}

              {/* Overlay with lock icon and purchase info */}
              <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
                <div className="text-center space-y-4 p-6">
                  <div className="flex items-center justify-center space-x-2 text-white">
                    <Lock className="h-12 w-12" />
                  </div>
                  <h3 className="text-xl font-semibold text-white">Premium Content</h3>
                  <p className="text-gray-200 text-sm">
                    This is a paid event. Purchase access to watch this content.
                  </p>

                  {/* Price Display */}
                  {eventPrice && eventPrice > 0 && (
                    <div className="text-center">
                      <div className="text-3xl font-bold text-white mb-2">
                        {eventCurrency?.toUpperCase() || 'USD'} {eventPrice}
                      </div>
                    </div>
                  )}

                  {/* Purchase Button */}
                  <Button
                    onClick={() => {
                      if (onPurchaseRequired) {
                        onPurchaseRequired();
                      } else {
                        toast({
                          title: "Purchase Required",
                          description: "Please purchase this event to watch the content.",
                          variant: "default",
                        });
                      }
                    }}
                    className="bg-primary hover:bg-primary/90 text-white px-8 py-3 text-lg"
                    size="lg"
                  >
                    <ShoppingCart className="h-5 w-5 mr-2" />
                    Purchase Now
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button
            onClick={() => fetchPlaybackToken()}
            className="mt-4"
            variant="outline"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!playbackData) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <Alert>
            <AlertDescription>No playback data available</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const streamUrl = getStreamUrl();

  return (
    <div className={className}>
      {/* Authorization Status */}
      <div className="mb-4 space-y-2">
        {playbackData.requiresAuthorization ? (
          <div className="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <Lock className="h-5 w-5 text-yellow-600" />
              <span className="text-yellow-800 font-medium">Premium Content</span>
            </div>
            {playbackData.expiresAt && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-yellow-700">
                  Expires: {formatExpirationTime(playbackData.expiresAt)}
                </span>
                {(tokenExpiring || refreshing) && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={manualRefresh}
                    disabled={refreshing}
                  >
                    {refreshing ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <RefreshCw className="h-4 w-4" />
                    )}
                  </Button>
                )}
              </div>
            )}
          </div>
        ) : (
          <div className="flex items-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-lg">
            <Unlock className="h-5 w-5 text-green-600" />
            <span className="text-green-800 font-medium">Free Content</span>
            {playbackData.message && (
              <span className="text-sm text-green-700">- {playbackData.message}</span>
            )}
          </div>
        )}

        {/* Token Expiring Warning */}
        {tokenExpiring && playbackData.requiresAuthorization && (
          <Alert variant="destructive">
            <AlertDescription className="flex items-center justify-between">
              <span>Your access is expiring soon!</span>
              <Button size="sm" onClick={manualRefresh} disabled={refreshing}>
                {refreshing ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                Renew Access
              </Button>
            </AlertDescription>
          </Alert>
        )}
      </div>

      {/* Video Player */}
      <Card>
        <CardContent className="p-0">
          {streamUrl ? (
            <div className="relative w-full" style={{ paddingBottom: '56.25%' }}>
              <iframe
                ref={iframeRef}
                src={streamUrl}
                className="absolute top-0 left-0 w-full h-full rounded-lg border-0"
                allowFullScreen
                allow="autoplay; fullscreen"
                title={playbackData.eventInfo?.title || 'Video Player'}
              />
            </div>
          ) : (
            <div className="flex items-center justify-center h-64 bg-gray-100 rounded-lg">
              <span className="text-gray-500">No stream URL available</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Event Information */}
      {playbackData.eventInfo && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <h3 className="font-semibold text-lg">{playbackData.eventInfo.title}</h3>
          {playbackData.eventInfo.price > 0 && (
            <p className="text-sm text-gray-600">
              Price: {playbackData.eventInfo.currency.toUpperCase()} {playbackData.eventInfo.price}
            </p>
          )}
          {playbackData.channelInfo && (
            <p className="text-sm text-gray-600">
              Channel: {playbackData.channelInfo.name}
            </p>
          )}
        </div>
      )}
    </div>
  );
};

export default AuthorizedVideoPlayer;