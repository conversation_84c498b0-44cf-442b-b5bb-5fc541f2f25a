"use client";

import React, { useState, useEffect } from 'react';
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Event } from "@/interfaces/types";
import {
  DollarSign,
  CreditCard,
  AlertCircle,
  TrendingUp,
  Clock,
  Wallet,
  ArrowUpRight,
  ArrowDownRight,
  Info,
  Download
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import {
  fetchChannelFinancials,
  fetchChannelTransactions,
  fetchCommissionRates,
  formatCurrency,
  formatFinancialDate,
  FinancialSummary,
  TransactionDetails,
  CommissionRates,
} from '@/services/financial-service';
import {
  createPayoutRequest,
  getChannelPayoutRequests,
} from '@/services/payout-service';

interface ContentCreatorFinancialsViewProps {
  event: Event;
}

export function ContentCreatorFinancialsView({ event }: ContentCreatorFinancialsViewProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [financials, setFinancials] = useState<FinancialSummary | null>(null);
  const [transactions, setTransactions] = useState<TransactionDetails[]>([]);
  const [totalTransactions, setTotalTransactions] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'available' | 'paid_out'>('all');
  const [commissionRates, setCommissionRates] = useState<CommissionRates>({
    platformPercentage: 20,
    creatorPercentage: 80,
    payoutDelayDays: 7,
    paymentProcessing: {
      stripePercentageFee: 2.9,
      stripeFixedFeeCents: 30,
      stripeFixedFee: 0.30,
    },
    streaming: {
      region: 'eu-west-1',
      regionName: 'Europe',
      advancedHdRatePerParticipantHour: 0.0225,
      standardRatePerParticipantHour: 0.0080,
    },
    description: 'Loading commission rates...'
  });
  const [payoutDialogOpen, setPayoutDialogOpen] = useState(false);
  const [payoutAmount, setPayoutAmount] = useState('');
  const [payoutMethod, setPayoutMethod] = useState('bank_transfer');
  const [processingPayout, setProcessingPayout] = useState(false);
  const [payoutRequests, setPayoutRequests] = useState<any[]>([]);
  const [totalPayoutRequests, setTotalPayoutRequests] = useState(0);
  const [payoutRequestsPage, setPayoutRequestsPage] = useState(1);

  const channelId = event.channel?.id;
  const transactionsPerPage = 10;

  // Load financial data
  useEffect(() => {
    if (channelId) {
      loadFinancialData();
      loadCommissionRates();
    }
  }, [channelId]);

  // Load transactions when page or filter changes
  useEffect(() => {
    if (channelId) {
      loadTransactions();
    }
  }, [channelId, currentPage, statusFilter]);

  // Load payout requests when page changes
  useEffect(() => {
    if (channelId) {
      loadPayoutRequests();
    }
  }, [channelId, payoutRequestsPage]);

  const loadFinancialData = async () => {
    if (!channelId) return;

    try {
      setLoading(true);
      const data = await fetchChannelFinancials(channelId);
      setFinancials(data);
    } catch (error) {
      console.error('Failed to load financial data:', error);
      toast({
        title: "Error",
        description: "Failed to load financial data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadTransactions = async () => {
    if (!channelId) return;

    try {
      const status = statusFilter === 'all' ? undefined : statusFilter;
      const data = await fetchChannelTransactions(channelId, currentPage, transactionsPerPage, status);
      setTransactions(data.transactions);
      setTotalTransactions(data.total);
    } catch (error) {
      console.error('Failed to load transactions:', error);
    }
  };

  const loadCommissionRates = async () => {
    try {
      const rates = await fetchCommissionRates();
      setCommissionRates(rates);
    } catch (error) {
      console.error('Failed to load commission rates:', error);
    }
  };

  const loadPayoutRequests = async () => {
    if (!channelId) return;

    try {
      const data = await getChannelPayoutRequests(channelId, payoutRequestsPage, transactionsPerPage);
      setPayoutRequests(data.requests);
      setTotalPayoutRequests(data.total);
    } catch (error) {
      console.error('Failed to load payout requests:', error);
    }
  };

  const handleRequestPayout = async () => {
    if (!channelId || !payoutAmount) return;

    try {
      setProcessingPayout(true);
      const amount = parseFloat(payoutAmount);

      if (isNaN(amount) || amount <= 0) {
        throw new Error('Invalid payout amount');
      }

      if (financials && amount > financials.availableBalance) {
        throw new Error('Payout amount exceeds available balance');
      }

      await createPayoutRequest(channelId, {
        amount,
        payoutMethod,
        payoutDetails: payoutMethod === 'bank_transfer' ? 'Bank details on file' : undefined
      });

      toast({
        title: "Success",
        description: `Payout request of ${formatCurrency(amount)} has been submitted.`,
      });

      setPayoutDialogOpen(false);
      setPayoutAmount('');
      loadFinancialData();
      loadTransactions();
      loadPayoutRequests();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to process payout request",
        variant: "destructive",
      });
    } finally {
      setProcessingPayout(false);
    }
  };

  const totalPages = Math.ceil(totalTransactions / transactionsPerPage);

  if (!channelId) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        Channel information not available
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!financials) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No financial data available
      </div>
    );
  }

  const availablePercentage = financials.totalRevenue > 0
    ? (financials.availableBalance / financials.totalRevenue) * 100
    : 0;

  return (
    <div className="space-y-6">
      {/* Commission Info Banner */}
      <Card className="p-4 bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800">
        <div className="flex items-start gap-2">
          <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
          <div className="text-sm">
            <p className="font-medium text-blue-900 dark:text-blue-100">Commission Structure</p>
            <p className="text-blue-700 dark:text-blue-300">
              {commissionRates.description}
            </p>
          </div>
        </div>
      </Card>

      {/* Financial Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex flex-col">
            <div className="flex items-center gap-2 text-muted-foreground mb-1">
              <CreditCard className="h-4 w-4" />
              <span className="text-sm">Total Revenue</span>
            </div>
            <span className="text-2xl font-bold">{formatCurrency(financials.totalRevenue)}</span>
            <div className="flex items-center gap-1 mt-1">
              <TrendingUp className="h-3 w-3 text-green-600" />
              <span className="text-xs text-green-600">Before commission</span>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex flex-col">
            <div className="flex items-center gap-2 text-muted-foreground mb-1">
              <Wallet className="h-4 w-4" />
              <span className="text-sm">Your Earnings</span>
            </div>
            <span className="text-2xl font-bold">{formatCurrency(financials.creatorRevenue)}</span>
            <div className="flex items-center gap-1 mt-1">
              <span className="text-xs text-muted-foreground">
                {commissionRates.creatorPercentage}% of revenue
              </span>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex flex-col">
            <div className="flex items-center gap-2 text-muted-foreground mb-1">
              <DollarSign className="h-4 w-4" />
              <span className="text-sm">Available Now</span>
            </div>
            <span className="text-2xl font-bold text-green-600">
              {formatCurrency(financials.availableBalance)}
            </span>
            <div className="flex items-center gap-1 mt-1">
              <span className="text-xs text-muted-foreground">
                Ready for withdrawal
              </span>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex flex-col">
            <div className="flex items-center gap-2 text-muted-foreground mb-1">
              <Clock className="h-4 w-4" />
              <span className="text-sm">Pending</span>
            </div>
            <span className="text-2xl font-bold text-yellow-600">
              {formatCurrency(financials.pendingBalance)}
            </span>
            <div className="flex items-center gap-1 mt-1">
              <span className="text-xs text-muted-foreground">
                Available in {commissionRates.payoutDelayDays} days
              </span>
            </div>
          </div>
        </Card>
      </div>

      {/* Progress Bar */}
      <Card className="p-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Balance Overview</span>
            <span className="font-medium">
              {availablePercentage.toFixed(1)}% Available
            </span>
          </div>
          <Progress value={availablePercentage} className="h-2" />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Paid Out: {formatCurrency(financials.totalPaidOut)}</span>
            <span>Total Earned: {formatCurrency(financials.creatorRevenue)}</span>
          </div>
        </div>
      </Card>

      {/* Action Buttons */}
      <div className="flex gap-4">
        <Dialog open={payoutDialogOpen} onOpenChange={setPayoutDialogOpen}>
          <DialogTrigger asChild>
            <Button
              variant="default"
              disabled={financials.availableBalance < 50}
            >
              <Wallet className="h-4 w-4 mr-2" />
              Request Payout
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Request Payout</DialogTitle>
              <DialogDescription>
                Request a payout of your available balance. Minimum payout is $50.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label>Available Balance</Label>
                <p className="text-2xl font-bold">{formatCurrency(financials.availableBalance)}</p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="amount">Payout Amount</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  min="50"
                  max={financials.availableBalance}
                  value={payoutAmount}
                  onChange={(e) => setPayoutAmount(e.target.value)}
                  placeholder="Enter amount"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="method">Payout Method</Label>
                <Select value={payoutMethod} onValueChange={setPayoutMethod}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                    <SelectItem value="paypal">PayPal</SelectItem>
                    <SelectItem value="stripe">Stripe</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setPayoutDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleRequestPayout}
                disabled={processingPayout || !payoutAmount || parseFloat(payoutAmount) < 50}
              >
                {processingPayout ? "Processing..." : "Request Payout"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Button variant="outline">
          <Download className="h-4 w-4 mr-2" />
          Export Report
        </Button>
      </div>

      {/* Transactions Table */}
      <Card className="p-4">
        <Tabs defaultValue="transactions" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="transactions">Transaction History</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="payouts">Payout Requests</TabsTrigger>
          </TabsList>

          <TabsContent value="transactions">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Transaction History</h3>
                <Select value={statusFilter} onValueChange={(value: any) => setStatusFilter(value)}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Transactions</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="available">Available</SelectItem>
                    <SelectItem value="paid_out">Paid Out</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Event</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Gross Amount</TableHead>
                    <TableHead>Net Amount</TableHead>
                    <TableHead>Your Earnings</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions.map((transaction, index) => (
                    <TableRow key={index}>
                      <TableCell>{formatFinancialDate(transaction.purchaseDate)}</TableCell>
                      <TableCell className="max-w-[200px]">
                        <div className="flex items-center gap-2">
                          <span className="truncate">{transaction.eventTitle}</span>
                          {transaction.eventEndTime && new Date(transaction.eventEndTime) > new Date() && (
                            <Badge variant="outline" className="text-xs">
                              Future Event
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{transaction.userEmail}</TableCell>
                      <TableCell>
                        <div className="space-y-0.5">
                          <div>{formatCurrency(transaction.purchaseAmount)}</div>
                          <div className="text-xs text-muted-foreground space-y-0.5">
                            {transaction.processingFee > 0 && (
                              <div>-{formatCurrency(transaction.processingFee)} processing</div>
                            )}
                            {transaction.streamingCharges && transaction.streamingCharges > 0 && (
                              <div>
                                -{formatCurrency(transaction.streamingCharges)} streaming
                                {transaction.participantHours && transaction.participantCount && (
                                  <span className="text-xs text-muted-foreground ml-1">
                                    ({transaction.participantCount} viewers × {(transaction.participantHours / transaction.participantCount).toFixed(2)} hrs)
                                  </span>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{formatCurrency(transaction.netAmount || transaction.purchaseAmount)}</TableCell>
                      <TableCell className="font-medium">
                        {formatCurrency(transaction.creatorEarnings)}
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col gap-1">
                          <Badge variant={
                            transaction.status === 'paid_out' ? 'default' :
                            transaction.status === 'available' ? 'secondary' :
                            'outline'
                          }>
                            {transaction.status === 'paid_out' ? 'Paid Out' :
                             transaction.status === 'available' ? 'Available' :
                             'Pending'}
                          </Badge>
                          {transaction.eventEndTime && new Date(transaction.eventEndTime) > new Date() &&
                           transaction.status !== 'paid_out' && (
                            <span className="text-xs text-muted-foreground">
                              After event ends
                            </span>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center gap-2 mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="py-2 px-4 text-sm">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="analytics">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="p-4">
                <h4 className="font-medium mb-3">Revenue Breakdown</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Gross Sales</span>
                    <span className="font-medium">{formatCurrency(financials.totalRevenue + financials.totalProcessingFees + financials.totalStreamingCharges)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Processing Fees ({commissionRates.paymentProcessing.stripePercentageFee}% + ${commissionRates.paymentProcessing.stripeFixedFee})</span>
                    <span className="font-medium text-red-600">-{formatCurrency(financials.totalProcessingFees)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Streaming Charges (${commissionRates.streaming.advancedHdRatePerParticipantHour}/viewer-hr)</span>
                    <span className="font-medium text-red-600">-{formatCurrency(financials.totalStreamingCharges)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Net Sales</span>
                    <span className="font-medium">{formatCurrency(financials.totalRevenue)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Platform Fee ({commissionRates.platformPercentage}%)</span>
                    <span className="font-medium text-red-600">-{formatCurrency(financials.platformRevenue)}</span>
                  </div>
                  <div className="border-t pt-2 flex justify-between">
                    <span className="text-sm font-medium">Your Earnings</span>
                    <span className="font-bold">{formatCurrency(financials.creatorRevenue)}</span>
                  </div>
                </div>
              </Card>

              <Card className="p-4">
                <h4 className="font-medium mb-3">Payout Status</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Total Paid Out</span>
                    <span className="font-medium">{formatCurrency(financials.totalPaidOut)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Available for Payout</span>
                    <span className="font-medium text-green-600">{formatCurrency(financials.availableBalance)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Pending ({commissionRates.payoutDelayDays} days)</span>
                    <span className="font-medium text-yellow-600">{formatCurrency(financials.pendingBalance)}</span>
                  </div>
                </div>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="payouts">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Payout Request History</h3>

              {payoutRequests.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No payout requests yet
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Request Date</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Method</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Processed Date</TableHead>
                      <TableHead>Notes</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {payoutRequests.map((request, index) => (
                      <TableRow key={index}>
                        <TableCell>{formatFinancialDate(request.requestedAt)}</TableCell>
                        <TableCell className="font-medium">{formatCurrency(request.amount)}</TableCell>
                        <TableCell>{request.payoutMethod}</TableCell>
                        <TableCell>
                          <Badge variant={
                            request.status === 'completed' ? 'default' :
                            request.status === 'approved' ? 'secondary' :
                            request.status === 'rejected' ? 'destructive' :
                            'outline'
                          }>
                            {request.status.toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {request.processedAt && formatFinancialDate(request.processedAt)}
                        </TableCell>
                        <TableCell className="max-w-[200px] truncate">
                          {request.rejectionReason || request.notes || request.payoutReference || '-'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}

              {/* Pagination */}
              {totalPayoutRequests > transactionsPerPage && (
                <div className="flex justify-center gap-2 mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPayoutRequestsPage(p => Math.max(1, p - 1))}
                    disabled={payoutRequestsPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="py-2 px-4 text-sm">
                    Page {payoutRequestsPage} of {Math.ceil(totalPayoutRequests / transactionsPerPage)}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPayoutRequestsPage(p => Math.min(Math.ceil(totalPayoutRequests / transactionsPerPage), p + 1))}
                    disabled={payoutRequestsPage === Math.ceil(totalPayoutRequests / transactionsPerPage)}
                  >
                    Next
                  </Button>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );
}
