import { Suspense } from "react";
import CreateEventPage from "@/views/events/CreateEventPage";

function CreateEventPageWithSuspense() {
  return (
    <Suspense fallback={
      <div className="container py-8">
        <div className="flex justify-center items-center h-60">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
        </div>
      </div>
    }>
      <CreateEventPage />
    </Suspense>
  );
}

export default function CreateEvent() {
  return <CreateEventPageWithSuspense />;
}
