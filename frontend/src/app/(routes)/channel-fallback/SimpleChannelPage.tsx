"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import dynamic from "next/dynamic";
import { convertNeo4jIntegers } from "@/utils/neo4j-utils";

// Dynamically import the real PublicChannelPage with ssr: false
const PublicChannelPage = dynamic(
  () => import("@/views/channels/PublicChannelPage"),
  { ssr: false, loading: () => <ChannelPageLoading /> }
);

// Loading component
function ChannelPageLoading() {
  return (
    <div className="p-8 text-center">
      <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mx-auto mb-4"></div>
      <p>Loading channel page...</p>
    </div>
  );
}

interface SafeChannelWrapperProps {
  id: string;
}

export default function SafeChannelWrapper({ id }: SafeChannelWrapperProps) {
  const router = useRouter();
  const [isReady, setIsReady] = useState(false);
  const [safeId, setSafeId] = useState<string>("");
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    try {
      // Verify we got a string ID
      console.log('SafeChannelWrapper - received ID:', id, 'Type:', typeof id);
      
      // Safety check
      if (!id || typeof id !== 'string') {
        setError('Invalid channel ID');
        return;
      }

      // Ensure we have a clean, validated string ID
      const processedId = String(id).trim();
      console.log('SafeChannelWrapper - processed ID:', processedId);
      
      // Set the safe ID for rendering
      setSafeId(processedId);
      
      // Small delay to ensure client-side rendering is ready
      const timer = setTimeout(() => {
        setIsReady(true);
      }, 100);
      
      return () => clearTimeout(timer);
    } catch (err) {
      console.error('Error in SafeChannelWrapper:', err);
      setError('An error occurred while preparing the channel page');
    }
  }, [id]);

  if (error) {
    return (
      <div className="p-8 text-center">
        <div className="text-red-500 mb-4">{error}</div>
        <button 
          onClick={() => router.push('/home')}
          className="px-4 py-2 bg-blue-500 text-white rounded"
        >
          Return to Home
        </button>
      </div>
    );
  }

  if (!isReady || !safeId) {
    return <ChannelPageLoading />;
  }

  // Now we safely render the real PublicChannelPage with a guaranteed string ID
  return <PublicChannelPage id={safeId} />;
}