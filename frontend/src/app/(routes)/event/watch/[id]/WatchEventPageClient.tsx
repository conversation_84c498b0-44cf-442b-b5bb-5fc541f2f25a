'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Calendar,
  Clock,
  Users,
  DollarSign,
  ArrowLeft,
  Heart,
  Share2,
  Eye
} from 'lucide-react';
import { AuthorizedVideoPlayer } from '@/components/video/AuthorizedVideoPlayer';
import { useToast } from '@/hooks/use-toast';
import { fetchEvent, checkEventAccess } from '@/services/event-service';
import { toggleLike } from '@/services/like-service';
import { incrementViewCount } from '@/services/view-service';
import { Event } from '@/interfaces/types';

const WatchEventPageClient: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const eventId = params.id as string;

  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasAccess, setHasAccess] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(0);
  const [viewCount, setViewCount] = useState(0);

  const { toast } = useToast();

  useEffect(() => {
    if (eventId) {
      loadEvent();
      checkAccess();
      recordView();
    }
  }, [eventId]);

  const loadEvent = async () => {
    try {
      setLoading(true);
      const eventData = await fetchEvent(eventId);
      setEvent(eventData);
      setIsLiked(eventData.isLiked || false);
      setLikeCount(eventData.likes || 0);
      setViewCount(eventData.views || 0);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load event');
      toast({
        title: "Error",
        description: "Failed to load event details",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const checkAccess = async () => {
    try {
      const hasAccessResult = await checkEventAccess(eventId);
      setHasAccess(hasAccessResult);
    } catch (err) {
      console.warn('Failed to check access:', err);
      setHasAccess(false);
    }
  };

  const recordView = async () => {
    try {
      const result = await incrementViewCount(eventId);
      setViewCount(result.count || 0);
    } catch (err) {
      console.warn('Failed to record view:', err);
    }
  };

  const handleLike = async () => {
    try {
      const result = await toggleLike(eventId);
      setIsLiked(result.liked);
      setLikeCount(result.count || 0);

      if (result.liked) {
        toast({
          title: "Event Liked",
          description: "Event added to your liked events",
        });
      } else {
        toast({
          title: "Removed Like",
          description: "Event removed from your liked events",
        });
      }
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to update like status",
        variant: "destructive",
      });
    }
  };

  const handleShare = () => {
    const url = window.location.href;
    if (navigator.share) {
      navigator.share({
        title: event?.title,
        text: event?.description,
        url: url,
      });
    } else {
      navigator.clipboard.writeText(url);
      toast({
        title: "Link Copied",
        description: "Event link copied to clipboard",
      });
    }
  };

  const handlePurchase = () => {
    router.push(`/event/${eventId}`);
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'live': { label: 'Live', variant: 'destructive' as const },
      'upcoming': { label: 'Upcoming', variant: 'secondary' as const },
      'ended': { label: 'Ended', variant: 'outline' as const },
      'published': { label: 'Published', variant: 'default' as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] ||
                  { label: status, variant: 'default' as const };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error || !event) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertDescription>{error || 'Event not found'}</AlertDescription>
        </Alert>
        <Button
          onClick={() => router.back()}
          className="mt-4"
          variant="outline"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Go Back
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <Button
          onClick={() => router.back()}
          variant="outline"
          size="sm"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleLike}
          >
            <Heart className={`h-4 w-4 mr-2 ${isLiked ? 'fill-red-500 text-red-500' : ''}`} />
            {likeCount}
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleShare}
          >
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content - Video Player */}
        <div className="lg:col-span-2 space-y-6">
          {/* Video Player with Authorization */}
          <AuthorizedVideoPlayer
            eventId={eventId}
            autoplay={false}
            eventThumbnail={event.imageUrl}
            eventTitle={event.title}
            eventPrice={event.price}
            eventCurrency={event.currency}
            onError={(error) => {
              toast({
                title: "Video Error",
                description: error,
                variant: "destructive",
              });
            }}
            onTokenRefresh={(token) => {
              console.log('Token refreshed:', token);
            }}
            onPurchaseRequired={handlePurchase}
          />

          {/* Event Details */}
          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <CardTitle className="text-2xl">{event.title}</CardTitle>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <div className="flex items-center space-x-1">
                      <Eye className="h-4 w-4" />
                      <span>{viewCount.toLocaleString()} views</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Heart className="h-4 w-4" />
                      <span>{likeCount.toLocaleString()} likes</span>
                    </div>
                  </div>
                </div>
                {getStatusBadge(event.status)}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-700 leading-relaxed">{event.description}</p>

              {event.tags && event.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {event.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Event Info */}
          <Card>
            <CardHeader>
              <CardTitle>Event Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <Calendar className="h-5 w-5 text-gray-500" />
                <div>
                  <div className="font-medium">Start</div>
                  <div className="text-sm text-gray-600">
                    {formatDateTime(event.startTime)}
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Clock className="h-5 w-5 text-gray-500" />
                <div>
                  <div className="font-medium">End</div>
                  <div className="text-sm text-gray-600">
                    {formatDateTime(event.endTime)}
                  </div>
                </div>
              </div>

              {event.price > 0 && (
                <div className="flex items-center space-x-3">
                  <DollarSign className="h-5 w-5 text-gray-500" />
                  <div>
                    <div className="font-medium">Price</div>
                    <div className="text-sm text-gray-600">
                      {event.currency.toUpperCase()} {event.price}
                    </div>
                  </div>
                </div>
              )}

              {event.category && (
                <div>
                  <div className="font-medium mb-1">Category</div>
                  <Badge variant="outline">{event.category}</Badge>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Channel Info */}
          <Card>
            <CardHeader>
              <CardTitle>Channel</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-3">
                {event.channel.imageUrl ? (
                  <img
                    src={event.channel.imageUrl}
                    alt={event.channel.name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center">
                    <Users className="h-6 w-6 text-gray-500" />
                  </div>
                )}
                <div>
                  <div className="font-medium">{event.channel.name}</div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => router.push(`/channel/${event.channel.id}`)}
                  >
                    View Channel
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default WatchEventPageClient;