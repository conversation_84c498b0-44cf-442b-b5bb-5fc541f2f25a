"use client";

import { useState, useEffect } from "react";
import EventDetailPage from "@/views/events/EventDetailPage";
import { neo4jIntegerToString, isNeo4jInteger, convertNeo4jIntegers } from "@/utils/neo4j-utils";

interface EventDetailWrapperProps {
  eventId: string | any; // Accept any to handle Neo4j integer objects
}

export default function EventDetailWrapper({ eventId }: EventDetailWrapperProps) {
  // Use state to ensure we render with a safe string value
  const [safeId, setSafeId] = useState<string>("");

  // Handle conversion in useEffect to ensure we only do this on the client
  useEffect(() => {
    console.log('EventDetailWrapper - eventId:', eventId);
    console.log('EventDetailWrapper - eventId type:', typeof eventId);

    // Convert Neo4j integer to string if needed
    let stringId = "";

    if (isNeo4jInteger(eventId)) {
      console.log('Converting Neo4j integer to string');
      stringId = neo4jIntegerToString(eventId) || "";
    } else {
      // Ensure it's a primitive string
      stringId = String(eventId);
    }

    console.log('EventDetailWrapper - converted eventId:', stringId);
    setSafeId(stringId);
  }, [eventId]);

  // Only render EventDetailPage when we have a safe string ID
  if (!safeId) {
    return <div>Loading...</div>;
  }

  return <EventDetailPage />;
}
