"use client";

import dynamic from "next/dynamic";

// Import the SafePublicChannelPage with dynamic import to avoid SSR issues
const SafeChannel = dynamic(
  () => import("@/views/custom/SafePublicChannelPage"),
  { 
    ssr: false, 
    loading: () => (
      <div className="p-8 text-center">
        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mx-auto mb-4"></div>
        <p>Loading channel page...</p>
      </div>
    )
  }
);

// Simple wrapper that doesn't pass any props
export default function ChannelPublicPage() {
  // Render the safe channel page without passing any props
  // It will get its own ID from sessionStorage
  return <SafeChannel />;
}