"use client";

import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import ChannelViewPage from "@/views/channels/ChannelViewPage";

export default function ChannelViewerPage() {
  const router = useRouter();
  const [channelId, setChannelId] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    try {
      // Get the ID from sessionStorage
      const storedId = sessionStorage.getItem('current_channel_id');
      console.log('Retrieved channel ID from sessionStorage:', storedId);

      if (storedId) {
        // Set the channel ID
        setChannelId(storedId);
      } else {
        setError('No channel ID found in session storage');
      }
    } catch (err) {
      console.error('Error retrieving channel ID:', err);
      setError('Failed to retrieve channel ID');
    } finally {
      setIsLoading(false);
    }
  }, []);

  if (isLoading) {
    return (
      <div className="p-8 text-center">
        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mx-auto mb-4"></div>
        <p>Loading channel viewer...</p>
      </div>
    );
  }

  if (error || !channelId) {
    return (
      <div className="p-8 text-center">
        <div className="text-red-500 mb-4">{error || 'Channel ID not found'}</div>
        <button
          onClick={() => router.push('/home')}
          className="px-4 py-2 bg-blue-500 text-white rounded"
        >
          Return to Home
        </button>
      </div>
    );
  }

  // Render the ChannelViewPage component with the safe string ID
  return <ChannelViewPage />;
}