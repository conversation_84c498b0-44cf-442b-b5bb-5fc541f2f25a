"use client";

import ChannelViewPage from "@/views/channels/ChannelViewPage";
import React, { use } from "react";
import { isNeo4jInteger, neo4jIntegerToString } from "@/utils/neo4j-utils";

type Props = {
  params: Promise<{ id: string | { low: number, high: number } }>
}

// Create a client-side wrapper component to handle the ID processing
function ChannelViewWrapper({ id }: { id: any }) {
  // Process the ID to ensure it's always a string
  let safeId: string;

  try {
    if (isNeo4jInteger(id)) {
      // Handle Neo4j integer object
      safeId = neo4jIntegerToString(id) || "";
    } else if (typeof id === 'object' && id !== null) {
      // Handle other objects
      if ('low' in id && typeof id.low === 'number') {
        safeId = String(id.low);
      } else {
        // Last resort
        safeId = String(Object.values(id)[0] || "");
      }
    } else {
      // Handle primitive values
      safeId = String(id);
    }

    // Log for debugging
    console.log('Processed channel ID:', safeId, 'Type:', typeof safeId);

    // Render the actual page component with the safe ID
    return <ChannelViewPage />;
  } catch (error) {
    console.error('Error processing channel ID:', error);
    return <div className="p-8 text-center text-red-500">Error processing channel ID</div>;
  }
}

export default function ChannelView({ params }: Props) {
  // Unwrap the params Promise using React.use() - must be outside try/catch
  const unwrappedParams = use(params);

  try {
    const id = unwrappedParams.id;

    // Log for debugging
    console.log('Raw channel ID from params:', id, 'Type:', typeof id);

    // Use the wrapper component to handle the ID processing
    return <ChannelViewWrapper id={id} />;
  } catch (error) {
    console.error('Error in ChannelView:', error);
    return <div className="p-8 text-center text-red-500">Error loading channel</div>;
  }
}
