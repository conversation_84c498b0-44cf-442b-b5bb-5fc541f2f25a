"use client";

import { use } from "react";
import ChannelViewPage from "@/views/channels/ChannelViewPage";
import React, { useState, useEffect } from "react";
import { isNeo4jInteger, neo4jIntegerToString } from "@/utils/neo4j-utils";

type Props = {
  params: Promise<{ id: string | { low: number, high: number } }>
}

export default function EditChannel({ params }: Props) {
  // Unwrap params outside of try/catch
  const unwrappedParams = use(params);

  // Use state to ensure we render with a safe string value
  const [channelId, setChannelId] = useState<string>("");
  const [error, setError] = useState<string | null>(null);

  // Process the ID in useEffect to ensure client-side execution
  useEffect(() => {
    try {
      const rawId = unwrappedParams.id;

      console.log('Raw channel/edit ID:', rawId, 'Type:', typeof rawId);

      // Safely process the ID to ensure it's always a string
      let processedId: string;

      if (isNeo4jInteger(rawId)) {
        // Handle Neo4j integer object using the proper utility function
        processedId = neo4jIntegerToString(rawId) || "";
        console.log('Processed Neo4j integer to:', processedId);
      } else if (typeof rawId === 'object' && rawId !== null) {
        // For safety, avoid JSON.stringify which can cause React rendering issues
        // Instead extract a usable ID or use a fallback
        if ('low' in rawId && typeof rawId.low === 'number') {
          processedId = String(rawId.low);
        } else {
          // Last resort - but this should be avoided
          processedId = String(Object.values(rawId)[0] || "");
        }
        console.log('Processed object to:', processedId);
      } else {
        // Handle primitive values
        processedId = String(rawId);
        console.log('Processed primitive to:', processedId);
      }

      // Set the processed ID to state
      setChannelId(processedId);
    } catch (err) {
      console.error('Error processing channel/edit ID:', err);
      setError('Failed to process channel ID');
    }
  }, [unwrappedParams]);

  // Show loading state while processing
  if (!channelId && !error) {
    return <div className="p-8 text-center">Loading channel editor...</div>;
  }

  // Show error state if processing failed
  if (error) {
    return <div className="p-8 text-center text-red-500">Error: {error}</div>;
  }

  // Render the channel edit page with the safely processed ID
  return <ChannelViewPage isEditing={true} />;
}
