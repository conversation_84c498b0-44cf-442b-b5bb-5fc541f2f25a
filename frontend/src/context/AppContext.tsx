
"use client";

import { createContext, useContext, useState, useEffect, ReactNode, Dispatch, SetStateAction } from "react";
import { User, Channel, Event } from "@/interfaces/types";
import { DEMO_CHANNELS, DEMO_EVENTS } from "@/lib/demo-data";

interface AppContextType {
  user: User | null;
  setUser: Dispatch<SetStateAction<User | null>>;
  channels: Channel[];
  setChannels: Dispatch<SetStateAction<Channel[]>>;
  events: Event[];
  setEvents: Dispatch<SetStateAction<Event[]>>;
}

// Extend User interface to include roles
interface ExtendedUser extends User {
  roles?: string[];
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export function AppProvider({ children }: { children: ReactNode }) {
  // Add a mock user with multiple roles
  const mockUser: ExtendedUser = {
    id: "user-1",
    email: "<EMAIL>",
    firstName: "Demo",
    lastName: "User",
    username: "demouser",
    createdAt: new Date().toISOString(),
    roles: ["user", "moderator"]
  };

  const [user, setUser] = useState<User | null>(mockUser);
  const [channels, setChannels] = useState<Channel[]>(DEMO_CHANNELS);
  const [events, setEvents] = useState<Event[]>(DEMO_EVENTS);

  return (
    <AppContext.Provider value={{ user, setUser, channels, setChannels, events, setEvents }}>
      {children}
    </AppContext.Provider>
  );
}

export function useAppContext() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error("useAppContext must be used within an AppProvider");
  }
  return context;
}
