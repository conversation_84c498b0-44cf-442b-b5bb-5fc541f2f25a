
"use client";

import { useRouter } from "next/navigation";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, Mail } from "lucide-react";

export default function RegisterSuccessPage() {
  const router = useRouter();

  return (
    <div className="container max-w-md py-10">
      <Card className="shadow-lg border-primary/10">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <CheckCircle className="w-16 h-16 text-primary" />
          </div>
          <CardTitle className="text-2xl">Registration Successful!</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-muted-foreground">
            Your account has been created successfully.
          </p>

          <div className="bg-muted p-4 rounded-lg flex items-center space-x-4">
            <Mail className="h-8 w-8 text-primary" />
            <div className="text-left">
              <h3 className="font-medium">Verify Your Email</h3>
              <p className="text-sm text-muted-foreground">
                We've sent a verification email to your inbox. Please verify your email address to activate your account.
              </p>
            </div>
          </div>

          <p className="text-sm text-muted-foreground">
            After verifying your email, you can log in to access your account.
          </p>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button onClick={() => router.push('/login')}>
            Proceed to Login
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
