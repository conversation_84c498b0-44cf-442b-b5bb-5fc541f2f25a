"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { LogIn, Mail, AlertTriangle, Loader2 } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, <PERSON>Footer, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { loginWithCredentials, initAuth, isAuthenticated } from "@/lib/auth";
import { fetchCurrentUser } from "@/services/user-service";
import { useAppContext } from "@/context/NextAppContext";
import { useToast } from "@/hooks/use-toast";
import { getTwoFactorMethods, TokenResponse } from "@/services/auth-service";
import { TwoFactorAuth } from "@/components/auth/TwoFactorAuth";
import { resendVerificationEmail } from "@/services/auth-service";

export default function LoginPage() {
  const router = useRouter();
  const { setUser } = useAppContext();
  const { toast } = useToast();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [loginLoading, setLoginLoading] = useState(false);
  const [otpCode, setOtpCode] = useState("");
  const [showOtp, setShowOtp] = useState(false);
  const [showEmailVerification, setShowEmailVerification] = useState(false);
  const [resendingEmail, setResendingEmail] = useState(false);
  const [showTwoFactor, setShowTwoFactor] = useState(false);
  const [twoFactorMethods, setTwoFactorMethods] = useState<string[]>([]);

  // Initialize auth and check if user is already authenticated
  useEffect(() => {
    const init = async () => {
      try {
        setLoading(true);
        const authenticated = await initAuth();

        if (authenticated) {
          // User is already authenticated, fetch user data
          try {
            const userData = await fetchCurrentUser();
            setUser(userData);
            router.push('/home');
          } catch (userError) {
            console.error('Failed to fetch user data:', userError);
            toast({
              title: "Authentication Error",
              description: "Failed to fetch user data. Please try again.",
              variant: "destructive",
            });
          }
        }
      } catch (error) {
        console.error('Authentication initialization failed', error);
        toast({
          title: "Authentication Error",
          description: "Failed to initialize authentication. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    init();
  }, [router, setUser, toast]);

  const handleEmailSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (email) {
      setShowPassword(true);
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoginLoading(true);

    try {
      // First check if 2FA methods are available
      const methods = await getTwoFactorMethods(email);

      if (methods.length > 0) {
        // 2FA is required, show 2FA component
        setTwoFactorMethods(methods);
        setShowTwoFactor(true);
        setLoginLoading(false);
        return;
      }

      // Use direct login with credentials instead of redirect
      const result = await loginWithCredentials(email, password, showOtp ? otpCode : undefined);

      if (result.success) {
        // Login successful, fetch user data
        const userData = await fetchCurrentUser();
        setUser(userData);

        // Check if email verification is needed
        if (result.needsEmailVerification) {
          toast({
            title: "Login successful",
            description: "Please verify your email to access all features.",
            variant: "default",
          });

          // Still redirect to home, but the email verification banner will be shown
          router.push('/home');
        } else {
          toast({
            title: "Login successful",
            description: "You have been logged in successfully.",
          });
          router.push('/home');
        }
      } else {
        // Check for specific error types
        const error = result.error as any;

        if (error?.code === 'EMAIL_NOT_VERIFIED') {
          // Email verification required
          setShowEmailVerification(true);
          toast({
            title: "Email verification required",
            description: error.message || "Please verify your email before logging in.",
            variant: "default",
          });
        } else if (error?.message?.includes('otp') || error?.message?.toLowerCase().includes('two-factor')) {
          // OTP required
          setShowOtp(true);
          toast({
            title: "Two-factor authentication required",
            description: "Please enter your one-time password.",
            variant: "default",
          });
        } else {
          // Generic error
          toast({
            title: "Login failed",
            description: error?.message || "Invalid username or password",
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      console.error('Login error:', error);
      toast({
        title: "Login failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoginLoading(false);
    }
  };

  const handleTwoFactorSuccess = async (tokens: TokenResponse) => {
    try {
      // Store tokens in localStorage with consistent keys
      localStorage.setItem('token', tokens.access_token);
      localStorage.setItem('refresh_token', tokens.refresh_token);

      // Fetch user data
      const userData = await fetchCurrentUser();
      setUser(userData);

      toast({
        title: "Login successful",
        description: "You have been logged in successfully.",
      });

      router.push('/home');
    } catch (error) {
      console.error('Failed to complete login after 2FA:', error);
      toast({
        title: "Login failed",
        description: "Failed to complete login. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleTwoFactorBack = () => {
    setShowTwoFactor(false);
    setTwoFactorMethods([]);
  };

  const handleDirectLogin = () => {
    // Show the email form instead of redirecting
    setShowPassword(false);
    setShowEmailVerification(false);
  };

  const handleResendVerification = async () => {
    if (!email) {
      toast({
        title: "Email required",
        description: "Please enter your email address to resend the verification email.",
        variant: "destructive",
      });
      return;
    }

    setResendingEmail(true);

    try {
      const result = await resendVerificationEmail(email);

      if (result.success) {
        toast({
          title: "Verification email sent",
          description: result.message,
          variant: "default",
        });

        if (result.alreadyVerified) {
          setShowEmailVerification(false);
        }
      } else {
        toast({
          title: "Failed to send verification email",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Failed to resend verification email:', error);
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setResendingEmail(false);
    }
  };

  if (loading) {
    return (
      <div className="container max-w-md py-10">
        <div className="flex justify-center items-center h-60">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  // Show 2FA component if needed
  if (showTwoFactor) {
    return (
      <div className="container max-w-md py-10">
        <TwoFactorAuth
          username={email}
          password={password}
          availableMethods={twoFactorMethods}
          onSuccess={handleTwoFactorSuccess}
          onBack={handleTwoFactorBack}
        />
      </div>
    );
  }

  return (
    <div className="container max-w-md py-10">
      <Card className="shadow-lg border-primary/10">
        <CardHeader>
          <CardTitle className="text-2xl text-center">Welcome Back</CardTitle>
        </CardHeader>
        <CardContent>
          {showEmailVerification ? (
            <div className="space-y-4">
              <Alert className="bg-yellow-50 border-yellow-200">
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                <AlertTitle className="text-yellow-800">Email verification required</AlertTitle>
                <AlertDescription className="text-yellow-700">
                  Your account requires email verification before you can log in. Please check your inbox for a verification email.
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <Label htmlFor="verification-email">Email</Label>
                <Input
                  id="verification-email"
                  type="email"
                  placeholder="Confirm your email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={resendingEmail}
                  required
                />
              </div>

              <div className="flex flex-col space-y-2">
                <Button
                  onClick={handleResendVerification}
                  disabled={resendingEmail}
                  className="w-full"
                >
                  {resendingEmail ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Mail className="mr-2 h-4 w-4" />
                      Resend Verification Email
                    </>
                  )}
                </Button>

                <Button
                  variant="outline"
                  onClick={() => setShowEmailVerification(false)}
                  className="w-full"
                >
                  Back to Login
                </Button>
              </div>
            </div>
          ) : (
            <Button
              variant="outline"
              className="w-full mb-6"
              onClick={handleDirectLogin}
            >
              <LogIn className="mr-2 h-4 w-4" />
              Continue with Email
            </Button>
          )}


          {!showEmailVerification && (
            <>
              {!showPassword ? (
                <form onSubmit={handleEmailSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter your email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                    />
                  </div>
                  <Button type="submit" className="w-full">Continue</Button>
                </form>
              ) : (
                <form onSubmit={handlePasswordSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="password">Password</Label>
                      <Button
                        variant="link"
                        className="p-0 h-auto text-xs"
                        onClick={(e) => {
                          e.preventDefault();
                          router.push('/forgot-password');
                        }}
                      >
                        Forgot password?
                      </Button>
                    </div>
                    <Input
                      id="password"
                      type="password"
                      placeholder="Enter your password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                    />
                  </div>
                  {showOtp && (
                    <div className="space-y-2">
                      <Label htmlFor="otp">One-Time Password</Label>
                      <Input
                        id="otp"
                        type="text"
                        placeholder="Enter your OTP code"
                        value={otpCode}
                        onChange={(e) => setOtpCode(e.target.value)}
                        required={showOtp}
                      />
                    </div>
                  )}

                  <Button
                    type="submit"
                    className="w-full"
                    disabled={loginLoading}
                  >
                    {loginLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Logging in...
                      </>
                    ) : (
                      "Login"
                    )}
                  </Button>
                </form>
              )}
            </>
          )}
        </CardContent>
        <CardFooter className="flex justify-center">
          <p className="text-sm text-muted-foreground">
            Don't have an account?{" "}
            <Button variant="link" className="p-0" onClick={() => router.push('/register')}>
              Register
            </Button>
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}