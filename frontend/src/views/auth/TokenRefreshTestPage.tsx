"use client";

import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { getToken, getRefreshToken, refreshToken, isTokenExpired, getTokenExpiration } from "@/lib/auth";
import { jwtDecode } from "jwt-decode";
import apiClient from "@/lib/api-client";

export default function TokenRefreshTestPage() {
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [refreshTokenValue, setRefreshTokenValue] = useState<string | null>(null);
  const [decodedToken, setDecodedToken] = useState<any>(null);
  const [expiryTime, setExpiryTime] = useState<string | null>(null);
  const [isExpired, setIsExpired] = useState<boolean>(false);
  const [apiResponse, setApiResponse] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshLoading, setRefreshLoading] = useState<boolean>(false);

  // Load token data on component mount
  useEffect(() => {
    updateTokenInfo();
  }, []);

  // Update token information
  const updateTokenInfo = () => {
    const token = getToken();
    const refreshTokenVal = getRefreshToken();
    setAccessToken(token);
    setRefreshTokenValue(refreshTokenVal);

    if (token) {
      try {
        const decoded = jwtDecode(token);
        setDecodedToken(decoded);
        
        const expiration = getTokenExpiration();
        if (expiration) {
          setExpiryTime(new Date(expiration).toLocaleString());
        }
        
        setIsExpired(isTokenExpired());
      } catch (error) {
        console.error("Failed to decode token:", error);
      }
    }
  };

  // Make a test API call
  const handleTestApiCall = async () => {
    setLoading(true);
    setApiResponse(null);
    
    try {
      // Make a call to a protected endpoint
      const response = await apiClient.get('/auth/me');
      setApiResponse(JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.error("API call failed:", error);
      setApiResponse(`Error: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(false);
      // Update token info after API call
      updateTokenInfo();
    }
  };

  // Manually refresh the token
  const handleManualRefresh = async () => {
    setRefreshLoading(true);
    
    try {
      const result = await refreshToken();
      if (result) {
        updateTokenInfo();
        setApiResponse("Token refreshed successfully");
      } else {
        setApiResponse("Token refresh failed");
      }
    } catch (error) {
      console.error("Token refresh failed:", error);
      setApiResponse(`Error: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setRefreshLoading(false);
    }
  };

  return (
    <div className="container py-10">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Token Refresh Test</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium mb-1">Access Token</h3>
              <div className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-20">
                {accessToken ? accessToken.substring(0, 20) + "..." : "No token found"}
              </div>
            </div>
            
            <div>
              <h3 className="font-medium mb-1">Refresh Token</h3>
              <div className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-20">
                {refreshTokenValue ? refreshTokenValue.substring(0, 20) + "..." : "No refresh token found"}
              </div>
            </div>
            
            <div>
              <h3 className="font-medium mb-1">Token Expiration</h3>
              <div className="bg-gray-100 p-2 rounded">
                <p>Expires at: {expiryTime || "Unknown"}</p>
                <p>Status: {isExpired ? "Expired" : "Valid"}</p>
              </div>
            </div>
            
            <div>
              <h3 className="font-medium mb-1">Decoded Token</h3>
              <div className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40">
                <pre>{decodedToken ? JSON.stringify(decodedToken, null, 2) : "No token to decode"}</pre>
              </div>
            </div>
            
            <div className="flex space-x-4">
              <Button onClick={handleTestApiCall} disabled={loading}>
                {loading ? "Loading..." : "Test API Call"}
              </Button>
              <Button onClick={handleManualRefresh} disabled={refreshLoading} variant="outline">
                {refreshLoading ? "Refreshing..." : "Manually Refresh Token"}
              </Button>
            </div>
            
            {apiResponse && (
              <div>
                <h3 className="font-medium mb-1">API Response</h3>
                <div className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40">
                  <pre>{apiResponse}</pre>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
