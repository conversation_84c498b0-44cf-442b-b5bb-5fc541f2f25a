
"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function MFAPage() {
  const router = useRouter();

  const [mfaType, setMfaType] = useState("authenticator");
  const [code, setCode] = useState("");

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // Simulate MFA verification
    router.push('/home');
  };

  return (
    <div className="container max-w-md py-10">
      <Card className="shadow-lg border-primary/10">
        <CardHeader>
          <CardTitle className="text-2xl text-center">Verification Required</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="mfaType">Verification Method</Label>
              <Select
                value={mfaType}
                onValueChange={setMfaType}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select verification method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="authenticator">Authenticator App</SelectItem>
                  <SelectItem value="email">Email Code</SelectItem>
                  <SelectItem value="passkey">Passkey</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="code">Verification Code</Label>
              <Input
                id="code"
                placeholder="Enter code"
                value={code}
                onChange={(e) => setCode(e.target.value)}
                required
              />
            </div>

            <Button type="submit" className="w-full">Verify</Button>
          </form>
        </CardContent>
        <CardFooter className="flex justify-center">
          <p className="text-sm text-muted-foreground">
            Didn't receive a code?{" "}
            <Button variant="link" className="p-0">
              Resend
            </Button>
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}
