
"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useAppContext } from "@/context/NextAppContext";
import { Event } from "@/lib/types";

import { Button } from "@/components/ui/button";
import { Heading } from "@/components/ui/heading";
import { Card } from "@/components/ui/card";
import { ArrowLeft, Calendar, UserX, DollarSign } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import { parseNeo4jDateTime, formatDate } from "@/utils/date-utils";

export default function AdminEventsPage() {
  const router = useRouter();
  const { events, setEvents } = useAppContext();
  const { toast } = useToast();

  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string | undefined>(undefined);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);

  const filteredEvents = events.filter(event => {
    const matchesSearch = searchQuery === "" ||
      event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (event.description && event.description.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesStatus = !statusFilter || statusFilter === "all" || event.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const totalPages = Math.ceil(filteredEvents.length / itemsPerPage);
  const paginatedEvents = filteredEvents.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handleToggleEventBlock = (eventId: string) => {
    setEvents(prevEvents => prevEvents.map(e => {
      if (e.id === eventId) {
        switch (e.status) {
          case 'draft':
            toast({
              title: "Event Published",
              description: "The event is now visible to all users."
            });
            return { ...e, status: 'published' };
          case 'published':
            toast({
              title: "Event Canceled",
              description: "The event has been canceled."
            });
            return { ...e, status: 'canceled' };
          case 'canceled':
            toast({
              title: "Event Drafted",
              description: "The event is now in draft mode."
            });
            return { ...e, status: 'draft' };
          default:
            return e;
        }
      }
      return e;
    }));
  };

  const handleRefund = (eventId: string) => {
    console.log('Processing refund for event:', eventId);
    toast({
      title: "Refund Initiated",
      description: "The refund process has been started for this event."
    });
  };

  const goToPrevPage = () => {
    setCurrentPage(prev => Math.max(1, prev - 1));
  };

  const goToNextPage = () => {
    setCurrentPage(prev => Math.min(totalPages, prev + 1));
  };

  return (
    <div className="container py-8">
      <Button
        variant="ghost"
        className="mb-6 flex items-center gap-2"
        onClick={() => router.push('/admin-portal')}
      >
        <ArrowLeft className="h-4 w-4" />
        Back to Admin Portal
      </Button>

      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <Heading
          title="Event Management"
          description="Manage all events on the platform"
        />

        <div className="flex items-center gap-2">
          <Badge variant="outline" className="flex gap-1">
            <Calendar className="h-3.5 w-3.5" />
            {filteredEvents.length} Events
          </Badge>
        </div>
      </div>

      <Card className="mb-6">
        <div className="p-4 flex flex-col sm:flex-row gap-4">
          <Input
            placeholder="Search events..."
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setCurrentPage(1);
            }}
            className="sm:max-w-xs"
          />

          <Select
            value={statusFilter}
            onValueChange={(value) => {
              setStatusFilter(value || undefined);
              setCurrentPage(1);
            }}
          >
            <SelectTrigger className="sm:max-w-xs">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All statuses</SelectItem>
              <SelectItem value="published">Published</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="canceled">Canceled</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </Card>

      <Card>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Event</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Views</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedEvents.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="h-24 text-center">
                    No events found.
                  </TableCell>
                </TableRow>
              ) : (
                paginatedEvents.map((event) => (
                  <TableRow key={event.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded overflow-hidden">
                          <img
                            src={typeof event.thumbnail === 'string' ? event.thumbnail : 'https://images.unsplash.com/photo-1605810230434-7631ac76ec81?q=80&w=600'}
                            alt={event.title}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div>
                          <div className="font-medium">{event.title}</div>
                          <div className="text-xs text-muted-foreground line-clamp-1">
                            {event.description}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {formatDate(event.date || event.startTime, 'MMM d, yyyy')}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          event.status === "published" ? "default" :
                          event.status === "canceled" ? "destructive" :
                          "secondary"
                        }
                      >
                        {event.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {event.views || 0}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRefund(event.id)}
                        >
                          <DollarSign className="mr-2 h-4 w-4" />
                          Refund
                        </Button>

                        <Button
                          variant={event.status === "draft" ? "outline" : "destructive"}
                          size="sm"
                          onClick={() => handleToggleEventBlock(event.id)}
                        >
                          {event.status === "draft" ? "Publish" :
                           event.status === "published" ? "Cancel" : "Draft"}
                          {event.status !== "draft" && <UserX className="ml-2 h-4 w-4" />}
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/event/${event.id}`)}
                        >
                          View
                        </Button>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => router.push(`/event/edit/${event.id}`)}
                        >
                          Edit
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {totalPages > 1 && (
          <div className="p-4">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={goToPrevPage}
                    className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>

                <PaginationItem>
                  <PaginationNext
                    onClick={goToNextPage}
                    className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </Card>
    </div>
  );
}
