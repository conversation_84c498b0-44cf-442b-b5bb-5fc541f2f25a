
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAppContext } from "@/context/NextAppContext";
import { User } from "@/interfaces/types";
import { fetchAllUsers, setUserRole, blockUser, unblockUser } from "@/services/admin-service";

import { Button } from "@/components/ui/button";
import { Heading } from "@/components/ui/heading";
import { Card } from "@/components/ui/card";
import { ArrowLeft, Shield, UserX, UsersIcon, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
} from "@/components/ui/pagination";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";

export default function UsersPage() {
  const router = useRouter();
  const { user } = useAppContext();
  const { toast } = useToast();

  const [searchQuery, setSearchQuery] = useState("");
  const [roleFilter, setRoleFilter] = useState<string | undefined>(undefined);
  const [currentPage, setCurrentPage] = useState(1);
  const [users, setUsers] = useState<User[]>([]);
  const [totalUsers, setTotalUsers] = useState(0);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  // Items per page
  const itemsPerPage = 10;

  // Fetch users from API
  useEffect(() => {
    const loadUsers = async () => {
      try {
        setLoading(true);
        const data = await fetchAllUsers(currentPage, itemsPerPage);
        setUsers(data.users);
        setTotalUsers(data.total);
      } catch (error) {
        console.error("Failed to fetch users:", error);
        toast({
          title: "Error",
          description: "Failed to load users. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadUsers();
  }, [currentPage, toast]);

  // Filter users based on search query and role filter
  const filteredUsers = users.filter(u => {
    const matchesSearch = searchQuery === "" ||
      `${u.firstName} ${u.lastName}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
      u.email.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesRole = !roleFilter ||
                        roleFilter === "all" ||
                        u.role === roleFilter;

    return matchesSearch && matchesRole;
  });

  // Calculate total pages
  const totalPages = Math.ceil(totalUsers / itemsPerPage);

  // When search or filter is applied, we're filtering client-side
  const isFiltering = searchQuery !== "" || (roleFilter && roleFilter !== "all");
  const displayedUsers = isFiltering
    ? filteredUsers
    : users;

  // If filtering, we need to paginate client-side
  const paginatedFilteredUsers = isFiltering
    ? filteredUsers.slice(
        (currentPage - 1) * itemsPerPage,
        currentPage * itemsPerPage
      )
    : displayedUsers;

  // Handle role toggle
  const handleRoleToggle = async (userId: string, role: string) => {
    try {
      setActionLoading(true);
      const updatedUser = await setUserRole(userId, role);

      // Update the user in the local state
      setUsers(users.map(u =>
        u.id === userId ? { ...u, role: updatedUser.role } : u
      ));

      toast({
        title: "Success",
        description: `User role updated to ${role}`,
      });
    } catch (error) {
      console.error("Failed to update user role:", error);
      toast({
        title: "Error",
        description: "Failed to update user role. Please try again.",
        variant: "destructive",
      });
    } finally {
      setActionLoading(false);
    }
  };

  // Handle user blocking/unblocking
  const handleToggleUserBlock = async (userId: string) => {
    try {
      setActionLoading(true);
      const userToUpdate = users.find(u => u.id === userId);

      if (!userToUpdate) return;

      const isBlocked = userToUpdate.status === "blocked";
      const updatedUser = isBlocked
        ? await unblockUser(userId)
        : await blockUser(userId);

      // Update the user in the local state
      setUsers(users.map(u =>
        u.id === userId ? { ...u, status: updatedUser.status } : u
      ));

      toast({
        title: "Success",
        description: `User ${isBlocked ? 'unblocked' : 'blocked'} successfully`,
      });
    } catch (error) {
      console.error("Failed to toggle user block status:", error);
      toast({
        title: "Error",
        description: "Failed to update user status. Please try again.",
        variant: "destructive",
      });
    } finally {
      setActionLoading(false);
    }
  };

  // Handle page navigation
  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(prev => prev - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(prev => prev + 1);
    }
  };

  // Handle direct page navigation
  const handlePageClick = (page: number) => {
    if (page !== currentPage) {
      setCurrentPage(page);
    }
  };

  // Available roles
  const availableRoles = ["admin", "moderator", "user"];

  return (
    <div className="container py-8">
      <Button
        variant="ghost"
        className="mb-6 flex items-center gap-2"
        onClick={() => router.push('/admin-portal')}
      >
        <ArrowLeft className="h-4 w-4" />
        Back to Admin Portal
      </Button>

      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <Heading
          title="User Management"
          description="Manage user accounts and permissions"
        />

        <div className="flex items-center gap-2">
          <Badge variant="outline" className="flex gap-1">
            <UsersIcon className="h-3.5 w-3.5" />
            {filteredUsers.length} Users
          </Badge>
        </div>
      </div>

      <Card className="mb-6">
        <div className="p-4 flex flex-col sm:flex-row gap-4">
          <Input
            placeholder="Search users..."
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setCurrentPage(1); // Reset page when search changes
            }}
            className="sm:max-w-xs"
          />

          <Select
            value={roleFilter}
            onValueChange={(value) => {
              setRoleFilter(value || undefined);
              setCurrentPage(1); // Reset page when filter changes
            }}
          >
            <SelectTrigger className="sm:max-w-xs">
              <SelectValue placeholder="Filter by role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All roles</SelectItem>
              <SelectItem value="admin">Admin</SelectItem>
              <SelectItem value="moderator">Moderator</SelectItem>
              <SelectItem value="user">User</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </Card>

      <Card>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={5} className="h-24 text-center">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      Loading users...
                    </div>
                  </TableCell>
                </TableRow>
              ) : paginatedFilteredUsers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="h-24 text-center">
                    No users found.
                  </TableCell>
                </TableRow>
              ) : (
                paginatedFilteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>{`${user.firstName} ${user.lastName}`}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <Badge
                        variant={user.role === "admin" ? "default" : (user.role === "moderator" ? "secondary" : "outline")}
                      >
                        {user.role || "user"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={user.status === "blocked" ? "destructive" : "outline"}
                      >
                        {user.status || "active"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              disabled={actionLoading}
                            >
                              {actionLoading ? (
                                <>
                                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                  Processing...
                                </>
                              ) : (
                                "Manage Role"
                              )}
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {availableRoles.map(role => (
                              <DropdownMenuItem
                                key={role}
                                onClick={() => handleRoleToggle(user.id, role)}
                                disabled={user.role === role}
                              >
                                {role === "admin" && <Shield className="mr-2 h-4 w-4" />}
                                {role === "moderator" && <Shield className="mr-2 h-4 w-4" />}
                                {role === "user" && <UsersIcon className="mr-2 h-4 w-4" />}
                                {role.charAt(0).toUpperCase() + role.slice(1)}
                                {user.role === role && " (Current)"}
                              </DropdownMenuItem>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>

                        <Button
                          variant={user.status === "blocked" ? "outline" : "destructive"}
                          size="sm"
                          onClick={() => handleToggleUserBlock(user.id)}
                          disabled={actionLoading}
                        >
                          {actionLoading ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <>
                              {user.status === "blocked" ? "Unblock" : "Block"}
                              {user.status !== "blocked" && <UserX className="ml-2 h-4 w-4" />}
                            </>
                          )}
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {totalPages > 1 && (
          <div className="p-4">
            <Pagination>
              <PaginationContent>
                <Button
                  onClick={handlePrevPage}
                  disabled={currentPage === 1 || loading || actionLoading}
                  variant="ghost"
                  size="sm"
                  className="gap-1"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Previous
                </Button>

                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  // Show pages around current page
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }

                  return (
                    <Button
                      key={pageNum}
                      onClick={() => handlePageClick(pageNum)}
                      variant={currentPage === pageNum ? "outline" : "ghost"}
                      size="sm"
                      disabled={loading || actionLoading}
                    >
                      {pageNum}
                    </Button>
                  );
                })}

                <Button
                  onClick={handleNextPage}
                  disabled={currentPage === totalPages || loading || actionLoading}
                  variant="ghost"
                  size="sm"
                  className="gap-1"
                >
                  Next
                  <ArrowLeft className="h-4 w-4 rotate-180" />
                </Button>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </Card>
    </div>
  );
}
