"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Channel } from "@/interfaces/types";

import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Heading } from "@/components/ui/heading";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, CheckIcon, XIcon, FileIcon, LayoutDashboardIcon, Tag, Calendar, Users, ListVideo, DollarSign } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ChannelApprovalForm } from "@/components/admin/ChannelApprovalForm";
import { DashboardStats } from "@/components/admin/DashboardStats";
import { DashboardCharts } from "@/components/admin/DashboardCharts";
import { fetchPendingChannels, approveChannel, rejectChannel } from "@/services/admin-service";
import { isAuthenticated, hasRole } from "@/lib/auth";
import { useToast } from "@/hooks/use-toast";

export default function AdminPortalPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [pendingChannels, setPendingChannels] = useState<Channel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [rejectionDialogOpen, setRejectionDialogOpen] = useState(false);
  const [selectedChannelId, setSelectedChannelId] = useState<string | null>(null);
  const [actionInProgress, setActionInProgress] = useState(false);

  // Load pending channels
  useEffect(() => {
    const init = async () => {
      try {
        setLoading(true);

        // Check if user is authenticated and has admin role
        if (!isAuthenticated()) {
          router.push("/login");
          return;
        }

        if (!hasRole('admin')) {
          router.push("/home");
          toast({
            title: "Access Denied",
            description: "You don't have permission to access the admin portal",
            variant: "destructive",
          });
          return;
        }

        // Load pending channels
        try {
          const response = await fetchPendingChannels(1, 10);
          // Handle both possible response formats
          const channels = response.channels || response || [];
          setPendingChannels(Array.isArray(channels) ? channels : []);
        } catch (apiError) {
          console.error('Failed to load pending channels', apiError);
          setPendingChannels([]);
        }
      } catch (err) {
        console.error('Failed to load admin data', err);
        setError('Failed to load admin data');
      } finally {
        setLoading(false);
      }
    };

    init();
  }, [router, toast]);

  const handleApprovalSubmit = async (data: { rating?: number; reason?: string; action: "approve" | "reject" }) => {
    if (!selectedChannelId) return;

    try {
      setActionInProgress(true);

      if (data.action === "approve") {
        await approveChannel(selectedChannelId);
        setPendingChannels(prev => prev.filter(channel => channel.id !== selectedChannelId));
        toast({
          title: "Success",
          description: "Channel approved successfully",
        });
      } else {
        await rejectChannel(selectedChannelId);
        setPendingChannels(prev => prev.filter(channel => channel.id !== selectedChannelId));
        toast({
          title: "Success",
          description: "Channel rejected",
        });
      }
    } catch (error) {
      console.error(`Failed to ${data.action} channel:`, error);
      toast({
        title: "Error",
        description: `Failed to ${data.action} channel. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setActionInProgress(false);
      setSelectedChannelId(null);
      setApprovalDialogOpen(false);
      setRejectionDialogOpen(false);
    }
  };

  if (loading) {
    return (
      <div className="container py-8">
        <div className="flex justify-center items-center h-60">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <Button
        variant="ghost"
        className="mb-6 flex items-center gap-2"
        onClick={() => router.push('/home')}
      >
        <ArrowLeft className="h-4 w-4" />
        Back to Home
      </Button>

      <Heading
        title="Admin Portal"
        description="Manage platform content and settings"
        className="mb-6"
      />

      <div className="mb-6">
        <DashboardStats />
      </div>

      <div className="mb-8">
        <DashboardCharts />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="cursor-pointer" onClick={() => router.push('/admin/users')}>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              <CardTitle className="text-lg">Users</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Manage user accounts and permissions
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer" onClick={() => router.push('/admin/channels')}>
          <CardHeader>
            <div className="flex items-center gap-2">
              <ListVideo className="h-5 w-5" />
              <CardTitle className="text-lg">Channels</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Manage all channels on the platform
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer" onClick={() => router.push('/admin/events')}>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              <CardTitle className="text-lg">Events</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Manage all events on the platform
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer" onClick={() => router.push('/admin/pricing-plans')}>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Tag className="h-5 w-5" />
              <CardTitle className="text-lg">Pricing Plans</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Manage pricing plans for content
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer" onClick={() => router.push('/admin/categories')}>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Tag className="h-5 w-5" />
              <CardTitle className="text-lg">Categories</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Manage content categories
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer" onClick={() => {}}>
          <CardHeader>
            <div className="flex items-center gap-2">
              <LayoutDashboardIcon className="h-5 w-5" />
              <CardTitle className="text-lg">Dashboard</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              View platform statistics
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer" onClick={() => router.push('/admin/payouts')}>
          <CardHeader>
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              <CardTitle className="text-lg">Payouts</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Manage creator payout requests
            </p>
          </CardContent>
        </Card>
      </div>

      <Heading
        title="Channel Approvals"
        description="Review and approve channel requests"
        className="mb-6"
      />

      {!pendingChannels || pendingChannels.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <h3 className="text-lg font-medium mb-2">No Pending Approvals</h3>
            <p className="text-muted-foreground">
              There are currently no channels waiting for approval.
            </p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => router.push('/admin/channels')}
            >
              View All Channels
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {pendingChannels?.slice(0, 3).map(channel => (
            <ChannelApprovalCard
              key={channel.id}
              channel={channel}
              onApprove={() => {
                setSelectedChannelId(channel.id);
                setApprovalDialogOpen(true);
              }}
              onReject={() => {
                setSelectedChannelId(channel.id);
                setRejectionDialogOpen(true);
              }}
              disabled={actionInProgress}
            />
          ))}

          {pendingChannels && pendingChannels.length > 3 && (
            <div className="text-center mt-4">
              <Button
                variant="outline"
                onClick={() => router.push('/admin/channels')}
              >
                View All Pending Channels ({pendingChannels?.length || 0})
              </Button>
            </div>
          )}
        </div>
      )}

      <ChannelApprovalForm
        channel={pendingChannels?.find(c => c.id === selectedChannelId) as Channel}
        type="approve"
        isOpen={approvalDialogOpen}
        onOpenChange={setApprovalDialogOpen}
        onSubmit={handleApprovalSubmit}
        disabled={actionInProgress}
      />

      <ChannelApprovalForm
        channel={pendingChannels?.find(c => c.id === selectedChannelId) as Channel}
        type="reject"
        isOpen={rejectionDialogOpen}
        onOpenChange={setRejectionDialogOpen}
        onSubmit={handleApprovalSubmit}
        disabled={actionInProgress}
      />
    </div>
  );
}

function ChannelApprovalCard({ channel, onApprove, onReject, disabled }: {
  channel: Channel;
  onApprove: () => void;
  onReject: () => void;
  disabled?: boolean;
}) {
  if (!channel) return null;

  const thumbnailUrl = channel.imageUrl || '/placeholder.svg';
  const bannerUrl = channel.bannerUrl || '/placeholder.svg';

  return (
    <Card>
      <div className="h-32 bg-muted relative">
        <img
          src={bannerUrl}
          alt={`${channel.name} banner`}
          className="w-full h-full object-cover"
        />
      </div>
      <CardHeader className="flex flex-row items-start gap-4">
        <div className="w-16 h-16 rounded overflow-hidden flex-shrink-0">
          <img
            src={thumbnailUrl}
            alt={channel.name}
            className="w-full h-full object-cover"
          />
        </div>
        <div className="flex flex-col flex-grow">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl mb-1">{channel.name}</CardTitle>
            <Badge variant="outline">
              {channel.status === 'pending_approval' ? 'Pending Approval' :
               channel.status === 'draft' ? 'Draft' : 'Pending Review'}
            </Badge>
          </div>
          <p className="text-sm text-muted-foreground line-clamp-2">{channel.description}</p>
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium mb-1">Channel Handle</h4>
            <p className="text-sm text-muted-foreground">
              {channel.handle || "No handle provided"}
            </p>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-1">Supporting Documents</h4>
            <div className="flex gap-2">
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <FileIcon className="h-4 w-4" />
                    View Documents
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Supporting Documents</DialogTitle>
                    <DialogDescription>
                      Review the uploaded documents for {channel.name}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="py-4">
                    <p className="text-muted-foreground text-sm">
                      In a real application, document previews would be displayed here.
                    </p>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>
      </CardContent>

      <Separator />

      <CardFooter className="flex justify-end gap-2 pt-4">
        <Button
          variant="outline"
          onClick={onReject}
          disabled={disabled}
          className="flex items-center gap-1"
        >
          <XIcon className="h-4 w-4" />
          Reject
        </Button>
        <Button
          onClick={onApprove}
          disabled={disabled}
          variant="default"
          className="flex items-center gap-1"
        >
          <CheckIcon className="h-4 w-4" />
          Approve
        </Button>
      </CardFooter>
    </Card>
  );
}