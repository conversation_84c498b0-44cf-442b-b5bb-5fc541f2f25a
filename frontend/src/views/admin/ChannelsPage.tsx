"use client";

import { useState, useEffect } from "react";
import { useAppContext } from "@/context/NextAppContext";
import { Channel } from "@/interfaces/types";
import { useRouter } from "next/navigation";
import { fetchAllChannels, approveChannel, rejectChannel } from "@/services/admin-service";
import { useToast } from "@/hooks/use-toast";

import { Button } from "@/components/ui/button";
import { Heading } from "@/components/ui/heading";
import { Card } from "@/components/ui/card";
import { ArrowLeft, CheckIcon, ListVideo, UserX, XIcon } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink
} from "@/components/ui/pagination";
import { Badge } from "@/components/ui/badge";
import { ChannelApprovalForm } from "@/components/admin/ChannelApprovalForm";

export default function AdminChannelsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { setChannels: setContextChannels } = useAppContext();

  const [channels, setChannels] = useState<Channel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Keep existing state variables
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string | undefined>(undefined);
  const [currentPage, setCurrentPage] = useState(1);

  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [rejectionDialogOpen, setRejectionDialogOpen] = useState(false);
  const [selectedChannelId, setSelectedChannelId] = useState<string | null>(null);

  const itemsPerPage = 5;

  // Load all channels from the API
  useEffect(() => {
    const loadChannels = async () => {
      try {
        setLoading(true);
        const { channels: channelsData } = await fetchAllChannels(1, 100); // Fetch up to 100 channels
        setChannels(channelsData);
        // Also update the context for consistency
        setContextChannels(channelsData);
      } catch (err) {
        console.error('Failed to load channels', err);
        setError('Failed to load channels');
        toast({
          title: "Error",
          description: "Failed to load channels",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    loadChannels();
  }, [toast, setContextChannels]);

  // Filter channels based on search query and status
  const filteredChannels = channels.filter(channel => {
    const matchesSearch = searchQuery === "" ||
      (channel.name || "").toLowerCase().includes(searchQuery.toLowerCase()) ||
      (channel.description && channel.description.toLowerCase().includes(searchQuery.toLowerCase()));

    // Handle special case for 'approved' filter to match both 'approved' and 'active' statuses
    const matchesStatus = !statusFilter ||
                          statusFilter === "all" ||
                          channel.status === statusFilter ||
                          (statusFilter === "approved" && channel.status === "active") ||
                          (statusFilter === "active" && channel.status === "approved");

    return matchesSearch && matchesStatus;
  });

  // Paginate channels
  const totalPages = Math.ceil(filteredChannels.length / itemsPerPage);
  const paginatedChannels = filteredChannels.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Handle approval submission
  const handleApprovalSubmit = async (data: { rating?: number; reason?: string; action: "approve" | "reject" }) => {
    if (!selectedChannelId) return;

    try {
      if (data.action === "approve") {
        await approveChannel(selectedChannelId);
        toast({
          title: "Channel Approved",
          description: "The channel has been approved successfully.",
          variant: "default",
        });
      } else {
        await rejectChannel(selectedChannelId);
        toast({
          title: "Channel Rejected",
          description: "The channel has been rejected successfully.",
          variant: "destructive",
        });
      }

      // Refresh the channels list
      const { channels: updatedChannels } = await fetchAllChannels(100, 0);
      setChannels(updatedChannels);
      setContextChannels(updatedChannels);
    } catch (err) {
      console.error(`Failed to ${data.action} channel:`, err);
      toast({
        title: "Error",
        description: `Failed to ${data.action} the channel.`,
        variant: "destructive",
      });
    }

    setSelectedChannelId(null);
  };

  // Handle channel blocking/unblocking
  const handleToggleChannelBlock = async (channelId: string) => {
    try {
      const channel = channels.find(c => c.id === channelId);
      if (!channel) return;

      if (channel.status === 'rejected') {
        // Unblock - set to pending
        await approveChannel(channelId); // This will set it to active
        toast({
          title: "Channel Unblocked",
          description: "The channel has been unblocked successfully.",
          variant: "default",
        });
      } else {
        // Block - set to rejected
        await rejectChannel(channelId);
        toast({
          title: "Channel Blocked",
          description: "The channel has been blocked successfully.",
          variant: "default",
        });
      }

      // Refresh the channels list
      const { channels: updatedChannels } = await fetchAllChannels(100, 0);
      setChannels(updatedChannels);
      setContextChannels(updatedChannels);
    } catch (err) {
      console.error("Failed to toggle channel block status:", err);
      toast({
        title: "Error",
        description: "Failed to update the channel status.",
        variant: "destructive",
      });
    }
  };

  // Handle page navigation
  const handlePrevPage = () => {
    setCurrentPage(prev => Math.max(1, prev - 1));
  };

  const handleNextPage = () => {
    setCurrentPage(prev => Math.min(totalPages, prev + 1));
  };

  return (
    <div className="container py-8">
      <Button
        variant="ghost"
        className="mb-6 flex items-center gap-2"
        onClick={() => router.push('/admin-portal')}
      >
        <ArrowLeft className="h-4 w-4" />
        Back to Admin Portal
      </Button>

      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <Heading
          title="Channel Management"
          description="Manage all channels on the platform"
        />

        <div className="flex items-center gap-2">
          <Badge variant="outline" className="flex gap-1">
            <ListVideo className="h-3.5 w-3.5" />
            {filteredChannels.length} Channels
          </Badge>
        </div>
      </div>

      <Card className="mb-6">
        <div className="p-4 flex flex-col sm:flex-row gap-4">
          <Input
            placeholder="Search channels..."
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setCurrentPage(1);
            }}
            className="sm:max-w-xs"
          />

          <Select
            value={statusFilter}
            onValueChange={(value) => {
              setStatusFilter(value);
              setCurrentPage(1);
            }}
          >
            <SelectTrigger className="sm:max-w-xs">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All statuses</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="pending_approval">Pending Approval</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </Card>

      <Card>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Channel</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Rating</TableHead>
                <TableHead>Subscribers</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={5} className="h-24 text-center">
                    <div className="flex justify-center items-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                      <span className="ml-2">Loading channels...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : error ? (
                <TableRow>
                  <TableCell colSpan={5} className="h-24 text-center text-destructive">
                    {error}
                  </TableCell>
                </TableRow>
              ) : paginatedChannels.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="h-24 text-center">
                    No channels found.
                  </TableCell>
                </TableRow>
              ) : (
                paginatedChannels.map((channel) => (
                  <TableRow key={channel.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded overflow-hidden">
                          <img
                            src={channel.imageUrl || channel.thumbnail || 'https://images.unsplash.com/photo-1605810230434-7631ac76ec81?q=80&w=600'}
                            alt={channel.name || ''}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div>
                          <div className="font-medium">{channel.name}</div>
                          <div className="text-xs text-muted-foreground line-clamp-1">
                            {channel.description}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          channel.status === "active" || channel.status === "approved" ? "default" :
                          channel.status === "rejected" ? "destructive" :
                          channel.status === "pending_approval" ? "outline" :
                          channel.status === "draft" ? "secondary" :
                          "outline"
                        }
                        className={
                          channel.status === "pending_approval" ? "bg-amber-100 text-amber-800 hover:bg-amber-100" :
                          channel.status === "draft" ? "bg-blue-100 text-blue-800 hover:bg-blue-100" :
                          ""
                        }
                      >
                        {channel.status === "active" || channel.status === "approved" ? "Approved" :
                         channel.status === "pending_approval" ? "Pending Approval" :
                         channel.status === "draft" ? "Draft" :
                         channel.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {channel.rating ? `${channel.rating}/5` : "Not rated"}
                    </TableCell>
                    <TableCell>
                      {channel.subscribersCount || 0}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        {(channel.status === "pending" || channel.status === "pending_approval" || channel.status === "draft") && (
                          <>
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => {
                                setSelectedChannelId(channel.id);
                                setRejectionDialogOpen(true);
                              }}
                            >
                              <XIcon className="h-4 w-4 mr-1" />
                              Reject
                            </Button>
                            <Button
                              variant="default"
                              size="sm"
                              onClick={() => {
                                setSelectedChannelId(channel.id);
                                setApprovalDialogOpen(true);
                              }}
                            >
                              <CheckIcon className="h-4 w-4 mr-1" />
                              Approve
                            </Button>
                          </>
                        )}

                        {(channel.status === "approved" || channel.status === "active" || channel.status === "rejected") && (
                          <Button
                            variant={channel.status === "rejected" ? "outline" : "destructive"}
                            size="sm"
                            onClick={() => handleToggleChannelBlock(channel.id)}
                          >
                            {channel.status === "rejected" ? "Unblock" : "Block"}
                            {channel.status !== "rejected" && <UserX className="ml-2 h-4 w-4" />}
                          </Button>
                        )}

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/channel/${channel.id}`)}
                        >
                          View
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {totalPages > 1 && (
          <div className="p-4">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <Button
                    onClick={handlePrevPage}
                    disabled={currentPage === 1}
                    variant="ghost"
                    size="sm"
                    className="gap-1"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    Previous
                  </Button>
                </PaginationItem>

                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <PaginationItem key={page}>
                    <Button
                      onClick={() => setCurrentPage(page)}
                      variant={currentPage === page ? "outline" : "ghost"}
                      size="sm"
                    >
                      {page}
                    </Button>
                  </PaginationItem>
                ))}

                <PaginationItem>
                  <Button
                    onClick={handleNextPage}
                    disabled={currentPage === totalPages}
                    variant="ghost"
                    size="sm"
                    className="gap-1"
                  >
                    Next
                    <ArrowLeft className="h-4 w-4 rotate-180" />
                  </Button>
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </Card>

      <ChannelApprovalForm
        channel={channels.find(c => c.id === selectedChannelId) as Channel}
        type="approve"
        isOpen={approvalDialogOpen}
        onOpenChange={setApprovalDialogOpen}
        onSubmit={handleApprovalSubmit}
      />

      <ChannelApprovalForm
        channel={channels.find(c => c.id === selectedChannelId) as Channel}
        type="reject"
        isOpen={rejectionDialogOpen}
        onOpenChange={setRejectionDialogOpen}
        onSubmit={handleApprovalSubmit}
      />
    </div>
  );
}
