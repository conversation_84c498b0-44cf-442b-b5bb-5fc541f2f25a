
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { ArrowLeft, Plus } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Heading } from "@/components/ui/heading";
import { PricingPlan } from "@/interfaces/types";
import { PricingPlanForm, PricingFormValues } from "@/components/admin/pricing/PricingPlanForm";
import { PricingPlanItem } from "@/components/admin/pricing/PricingPlanItem";
import {
  fetchPricingPlans,
  createPricingPlan,
  updatePricingPlan,
  deletePricingPlan
} from "@/services/pricing-plan-service";

export default function PricingPlansPage() {
  const router = useRouter();
  const [plans, setPlans] = useState<PricingPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);

  // Load pricing plans from API
  useEffect(() => {
    const loadPricingPlans = async () => {
      try {
        setLoading(true);
        const data = await fetchPricingPlans();
        setPlans(data);
        setError(null);
      } catch (err) {
        console.error('Failed to load pricing plans:', err);
        setError('Failed to load pricing plans. Please try again.');
        toast.error('Failed to load pricing plans');
      } finally {
        setLoading(false);
      }
    };

    loadPricingPlans();
  }, []);

  const onSubmit = async (data: PricingFormValues) => {
    try {
      if (isEditing && editingId) {
        // Update existing plan
        await updatePricingPlan(editingId, {
          price: data.price,
          vatAmount: data.vatAmount,
          currency: data.currency,
          product: data.product,
          countryIso2: data.countryIso2,
        });

        // Refresh the pricing plans list
        const updatedPlans = await fetchPricingPlans();
        setPlans(updatedPlans);

        toast.success("Pricing plan updated successfully");
      } else {
        // Create new plan
        await createPricingPlan({
          price: data.price,
          vatAmount: data.vatAmount,
          currency: data.currency,
          product: data.product,
          countryIso2: data.countryIso2,
        });

        // Refresh the pricing plans list
        const updatedPlans = await fetchPricingPlans();
        setPlans(updatedPlans);

        toast.success("Pricing plan created successfully");
      }

      resetAndCloseDialog();
    } catch (error) {
      console.error('Failed to save pricing plan:', error);
      toast.error(isEditing ? "Failed to update pricing plan" : "Failed to create pricing plan");
    }
  };

  const handleEdit = (plan: PricingPlan) => {
    setIsEditing(true);
    setEditingId(plan.id);
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    try {
      await deletePricingPlan(id);

      // Refresh the pricing plans list
      const updatedPlans = await fetchPricingPlans();
      setPlans(updatedPlans);

      toast.success("Pricing plan deleted successfully");
    } catch (error) {
      console.error('Failed to delete pricing plan:', error);
      toast.error("Failed to delete pricing plan");
    }
  };

  const resetAndCloseDialog = () => {
    setIsEditing(false);
    setEditingId(null);
    setIsDialogOpen(false);
  };

  // Show loading state
  if (loading) {
    return (
      <div className="container py-8">
        <Button
          variant="ghost"
          className="mb-6 flex items-center gap-2"
          onClick={() => router.push('/admin-portal')}
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Admin Portal
        </Button>
        <div className="flex justify-center items-center h-60">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="container py-8">
        <Button
          variant="ghost"
          className="mb-6 flex items-center gap-2"
          onClick={() => router.push('/admin-portal')}
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Admin Portal
        </Button>
        <Card>
          <CardContent className="text-center py-12">
            <h3 className="text-lg font-medium mb-2 text-destructive">Error Loading Pricing Plans</h3>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <Button
        variant="ghost"
        className="mb-6 flex items-center gap-2"
        onClick={() => router.push('/admin-portal')}
      >
        <ArrowLeft className="h-4 w-4" />
        Back to Admin Portal
      </Button>

      <div className="flex justify-between items-center mb-6">
        <Heading
          title="Pricing Plans"
          description="Manage pricing plans for video content"
        />
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => {
              setIsEditing(false);
              setEditingId(null);
            }}>
              <Plus className="mr-2 h-4 w-4" />
              Add Plan
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>{isEditing ? "Edit Pricing Plan" : "Create Pricing Plan"}</DialogTitle>
              <DialogDescription>
                {isEditing
                  ? "Update the pricing plan details"
                  : "Set up a new pricing plan for video content"}
              </DialogDescription>
            </DialogHeader>
            <PricingPlanForm
              onSubmit={onSubmit}
              onCancel={resetAndCloseDialog}
              isEditing={isEditing}
              defaultValues={editingId
                ? plans.find(p => p.id === editingId)
                : undefined
              }
            />
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Pricing Plans</CardTitle>
          <CardDescription>
            All available pricing plans for video content
          </CardDescription>
        </CardHeader>
        <CardContent>
          {plans.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No pricing plans yet</p>
            </div>
          ) : (
            <div className="space-y-4">
              {plans.map((plan) => (
                <PricingPlanItem
                  key={plan.id}
                  plan={plan}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
