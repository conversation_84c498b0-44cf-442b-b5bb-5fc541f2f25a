"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import {
  getAllPayoutRequests,
  getPayoutStatistics,
  approvePayoutRequest,
  rejectPayoutRequest,
  completePayoutRequest,
  PayoutRequest,
  PayoutStatistics,
  getPayoutStatusColor,
  formatPayoutMethod,
} from '@/services/payout-service';
import { formatCurrency, formatFinancialDate } from '@/services/financial-service';
import {
  Check,
  X,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
} from 'lucide-react';

export default function PayoutsPage() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [payoutRequests, setPayoutRequests] = useState<PayoutRequest[]>([]);
  const [statistics, setStatistics] = useState<PayoutStatistics | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('pending');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalRequests, setTotalRequests] = useState(0);
  const [selectedRequest, setSelectedRequest] = useState<PayoutRequest | null>(null);
  const [showApproveDialog, setShowApproveDialog] = useState(false);
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [showCompleteDialog, setShowCompleteDialog] = useState(false);
  const [approveNotes, setApproveNotes] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  const [payoutReference, setPayoutReference] = useState('');
  const [processing, setProcessing] = useState(false);

  const requestsPerPage = 20;

  useEffect(() => {
    loadStatistics();
  }, []);

  useEffect(() => {
    loadPayoutRequests();
  }, [statusFilter, currentPage]);

  const loadStatistics = async () => {
    try {
      const stats = await getPayoutStatistics();
      setStatistics(stats);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load payout statistics",
        variant: "destructive",
      });
    }
  };

  const loadPayoutRequests = async () => {
    try {
      setLoading(true);
      const { requests, total } = await getAllPayoutRequests(
        statusFilter === 'all' ? undefined : statusFilter,
        currentPage,
        requestsPerPage
      );
      setPayoutRequests(requests);
      setTotalRequests(total);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load payout requests",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async () => {
    if (!selectedRequest) return;

    try {
      setProcessing(true);
      await approvePayoutRequest(selectedRequest.id, approveNotes);
      toast({
        title: "Success",
        description: "Payout request approved successfully",
      });
      setShowApproveDialog(false);
      setApproveNotes('');
      setSelectedRequest(null);
      loadPayoutRequests();
      loadStatistics();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to approve payout request",
        variant: "destructive",
      });
    } finally {
      setProcessing(false);
    }
  };

  const handleReject = async () => {
    if (!selectedRequest || !rejectionReason.trim()) return;

    try {
      setProcessing(true);
      await rejectPayoutRequest(selectedRequest.id, rejectionReason);
      toast({
        title: "Success",
        description: "Payout request rejected",
      });
      setShowRejectDialog(false);
      setRejectionReason('');
      setSelectedRequest(null);
      loadPayoutRequests();
      loadStatistics();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to reject payout request",
        variant: "destructive",
      });
    } finally {
      setProcessing(false);
    }
  };

  const handleComplete = async () => {
    if (!selectedRequest || !payoutReference.trim()) return;

    try {
      setProcessing(true);
      await completePayoutRequest(selectedRequest.id, payoutReference);
      toast({
        title: "Success",
        description: "Payout marked as completed",
      });
      setShowCompleteDialog(false);
      setPayoutReference('');
      setSelectedRequest(null);
      loadPayoutRequests();
      loadStatistics();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to complete payout",
        variant: "destructive",
      });
    } finally {
      setProcessing(false);
    }
  };

  const totalPages = Math.ceil(totalRequests / requestsPerPage);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Payout Management</h1>
        <Button onClick={() => { loadPayoutRequests(); loadStatistics(); }}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
      </div>

      {/* Statistics Cards */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.pending.count}</div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(statistics.pending.amount)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Approved</CardTitle>
              <CheckCircle className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.approved.count}</div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(statistics.approved.amount)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <DollarSign className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.completed.count}</div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(statistics.completed.amount)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Rejected</CardTitle>
              <XCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.rejected.count}</div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(statistics.rejected.amount)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total</CardTitle>
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.total.count}</div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(statistics.total.amount)}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Payout Requests Table */}
      <Card>
        <CardHeader>
          <CardTitle>Payout Requests</CardTitle>
          <Tabs value={statusFilter} onValueChange={setStatusFilter}>
            <TabsList>
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="pending">Pending</TabsTrigger>
              <TabsTrigger value="approved">Approved</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
              <TabsTrigger value="rejected">Rejected</TabsTrigger>
            </TabsList>
          </Tabs>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Request Date</TableHead>
                    <TableHead>Channel</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Method</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Processed By</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {payoutRequests.map((request) => (
                    <TableRow key={request.id}>
                      <TableCell>{formatFinancialDate(request.requestedAt)}</TableCell>
                      <TableCell>{request.channelName}</TableCell>
                      <TableCell className="font-medium">{formatCurrency(request.amount)}</TableCell>
                      <TableCell>{formatPayoutMethod(request.payoutMethod)}</TableCell>
                      <TableCell>
                        <Badge variant={getPayoutStatusColor(request.status) as any}>
                          {request.status.toUpperCase()}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {request.processedAt && (
                          <span className="text-sm text-muted-foreground">
                            {formatFinancialDate(request.processedAt)}
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          {request.status === 'pending' && (
                            <>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedRequest(request);
                                  setShowApproveDialog(true);
                                }}
                              >
                                <Check className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedRequest(request);
                                  setShowRejectDialog(true);
                                }}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </>
                          )}
                          {request.status === 'approved' && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSelectedRequest(request);
                                setShowCompleteDialog(true);
                              }}
                            >
                              <DollarSign className="h-4 w-4 mr-1" />
                              Complete
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center gap-2 mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="py-2 px-4 text-sm">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Approve Dialog */}
      <Dialog open={showApproveDialog} onOpenChange={setShowApproveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve Payout Request</DialogTitle>
            <DialogDescription>
              Approve payout of {selectedRequest && formatCurrency(selectedRequest.amount)} to {selectedRequest?.channelName}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="notes">Notes (optional)</Label>
              <Textarea
                id="notes"
                value={approveNotes}
                onChange={(e) => setApproveNotes(e.target.value)}
                placeholder="Add any notes about this approval"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowApproveDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleApprove} disabled={processing}>
              {processing ? 'Processing...' : 'Approve'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Dialog */}
      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Payout Request</DialogTitle>
            <DialogDescription>
              Reject payout request from {selectedRequest?.channelName}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="reason">Rejection Reason</Label>
              <Textarea
                id="reason"
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                placeholder="Please provide a reason for rejection"
                required
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRejectDialog(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleReject}
              disabled={processing || !rejectionReason.trim()}
            >
              {processing ? 'Processing...' : 'Reject'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Complete Dialog */}
      <Dialog open={showCompleteDialog} onOpenChange={setShowCompleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Complete Payout</DialogTitle>
            <DialogDescription>
              Mark payout of {selectedRequest && formatCurrency(selectedRequest.amount)} as completed
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="reference">Payment Reference</Label>
              <Input
                id="reference"
                value={payoutReference}
                onChange={(e) => setPayoutReference(e.target.value)}
                placeholder="Transaction ID or reference number"
                required
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCompleteDialog(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleComplete}
              disabled={processing || !payoutReference.trim()}
            >
              {processing ? 'Processing...' : 'Mark as Completed'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}