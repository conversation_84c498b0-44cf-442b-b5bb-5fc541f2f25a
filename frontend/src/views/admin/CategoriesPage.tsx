"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Heading } from "@/components/ui/heading";
import { ArrowLeft, Pencil, Plus, Trash2 } from "lucide-react";
import { toast } from "sonner";

import { Category } from "@/interfaces/types";
import { fetchCategories, createCategory, updateCategory, deleteCategory } from "@/services/category-service";

const formSchema = z.object({
  name: z.string().min(2, "Category name must be at least 2 characters")
});

type FormValues = z.infer<typeof formSchema>;

export default function CategoriesPage() {
  const router = useRouter();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [selectedThumbnail, setSelectedThumbnail] = useState<File | null>(null);

  // Load categories from API
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setLoading(true);
        const data = await fetchCategories();

        // Map the API response to match our component's expected format
        const formattedCategories = data.map(category => ({
          id: category.id,
          name: category.name,
          thumbnail: category.imageUrl || category.thumbnail || "https://placehold.co/300x200"
        }));

        setCategories(formattedCategories);
        setError(null);
      } catch (err) {
        console.error('Failed to load categories:', err);
        setError('Failed to load categories. Please try again.');
        toast.error('Failed to load categories');
      } finally {
        setLoading(false);
      }
    };

    loadCategories();
  }, []);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: ""
    },
  });

  const onSubmit = async (data: FormValues) => {
    try {
      if (isEditing && editingId) {
        // Update existing category
        await updateCategory(editingId, { name: data.name }, selectedThumbnail || undefined);

        // Refresh the categories list
        const updatedCategories = await fetchCategories();
        const formattedCategories = updatedCategories.map(category => ({
          id: category.id,
          name: category.name,
          thumbnail: category.imageUrl || category.thumbnail || "https://placehold.co/300x200"
        }));
        setCategories(formattedCategories);

        toast.success("Category updated successfully");
      } else {
        // Create new category
        await createCategory({ name: data.name }, selectedThumbnail || undefined);

        // Refresh the categories list
        const updatedCategories = await fetchCategories();
        const formattedCategories = updatedCategories.map(category => ({
          id: category.id,
          name: category.name,
          thumbnail: category.imageUrl || category.thumbnail || "https://placehold.co/300x200"
        }));
        setCategories(formattedCategories);

        toast.success("Category created successfully");
      }

      resetAndCloseDialog();
    } catch (error) {
      console.error('Failed to save category:', error);
      toast.error(isEditing ? "Failed to update category" : "Failed to create category");
    }
  };

  const handleEdit = (category: Category) => {
    setIsEditing(true);
    setEditingId(category.id);
    setSelectedThumbnail(null);

    form.reset({
      name: category.name
    });

    setIsDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteCategory(id);

      // Refresh the categories list
      const updatedCategories = await fetchCategories();
      const formattedCategories = updatedCategories.map(category => ({
        id: category.id,
        name: category.name,
        thumbnail: category.imageUrl || category.thumbnail || "https://placehold.co/300x200"
      }));
      setCategories(formattedCategories);

      toast.success("Category deleted successfully");
    } catch (error) {
      console.error('Failed to delete category:', error);
      toast.error("Failed to delete category");
    }
  };

  const resetAndCloseDialog = () => {
    form.reset();
    setIsEditing(false);
    setEditingId(null);
    setSelectedThumbnail(null);
    setIsDialogOpen(false);
  };

  const handleThumbnailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files?.length) return;
    setSelectedThumbnail(e.target.files[0]);
  };

  // Show loading state
  if (loading) {
    return (
      <div className="container py-8">
        <Button
          variant="ghost"
          className="mb-6 flex items-center gap-2"
          onClick={() => router.push('/admin-portal')}
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Admin Portal
        </Button>
        <div className="flex justify-center items-center h-60">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="container py-8">
        <Button
          variant="ghost"
          className="mb-6 flex items-center gap-2"
          onClick={() => router.push('/admin-portal')}
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Admin Portal
        </Button>
        <Card>
          <CardContent className="text-center py-12">
            <h3 className="text-lg font-medium mb-2 text-destructive">Error Loading Categories</h3>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <Button
        variant="ghost"
        className="mb-6 flex items-center gap-2"
        onClick={() => router.push('/admin-portal')}
      >
        <ArrowLeft className="h-4 w-4" />
        Back to Admin Portal
      </Button>

      <div className="flex justify-between items-center mb-6">
        <Heading
          title="Categories"
          description="Manage content categories"
        />
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => {
              setIsEditing(false);
              setEditingId(null);
              form.reset({ name: "" });
            }}>
              <Plus className="mr-2 h-4 w-4" />
              Add Category
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>{isEditing ? "Edit Category" : "Create Category"}</DialogTitle>
              <DialogDescription>
                {isEditing
                  ? "Update category details"
                  : "Create a new content category"}
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter category name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-2">
                  <FormLabel>Thumbnail</FormLabel>
                  <Input
                    type="file"
                    accept="image/*"
                    onChange={handleThumbnailChange}
                  />
                  {selectedThumbnail && (
                    <div className="mt-2 text-sm text-muted-foreground">
                      Selected: {selectedThumbnail.name}
                    </div>
                  )}
                  {isEditing && !selectedThumbnail && (
                    <div className="mt-2 text-sm">
                      <p className="text-muted-foreground">Current thumbnail will be kept if no new image is selected</p>
                    </div>
                  )}
                </div>

                <DialogFooter>
                  <Button type="button" variant="outline" onClick={resetAndCloseDialog}>
                    Cancel
                  </Button>
                  <Button type="submit">
                    {isEditing ? "Update" : "Create"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Categories</CardTitle>
          <CardDescription>
            All content categories
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {categories.map((category) => (
              <Card key={category.id}>
                <div className="aspect-video w-full overflow-hidden">
                  <img
                    src={category.thumbnail}
                    alt={category.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardFooter className="flex items-center justify-between p-4">
                  <div className="font-medium">{category.name}</div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" onClick={() => handleEdit(category)}>
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleDelete(category.id)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
