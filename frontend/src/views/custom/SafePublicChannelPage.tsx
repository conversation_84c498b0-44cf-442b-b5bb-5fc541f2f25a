"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAppContext } from "@/context/NextAppContext";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Calendar, Bell, Users } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { fetchChannel } from "@/services/channel-service";
import { fetchPublicChannelEvents } from "@/services/event-service";
import {
  checkSubscription,
  subscribeToChannel,
  unsubscribeFromChannel
} from "@/services/subscription-service";
import { isAuthenticated } from "@/lib/auth";
import { neo4jIntegerToString, convertNeo4jIntegers, isNeo4jInteger } from "@/utils/neo4j-utils";

// This is a safer version of the PublicChannelPage that handles Neo4j objects carefully
export default function SafePublicChannelPage() {
  const router = useRouter();
  const { user } = useAppContext();

  // Get the ID directly from sessionStorage
  const [channelId, setChannelId] = useState<string>("");
  const [channel, setChannel] = useState<any>(null);
  const [events, setEvents] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [subscriberCount, setSubscriberCount] = useState(0);
  const [activeTab, setActiveTab] = useState<string>("all");

  // Get the channel ID from sessionStorage
  useEffect(() => {
    try {
      const storedId = sessionStorage.getItem('current_channel_id');
      console.log('Retrieved channel ID from sessionStorage:', storedId);

      if (storedId) {
        setChannelId(String(storedId));
      } else {
        setError('Channel ID not found');
        setLoading(false);
      }
    } catch (err) {
      console.error('Error retrieving channel ID:', err);
      setError('Failed to retrieve channel ID');
      setLoading(false);
    }
  }, []);

  // Load channel data once we have the ID
  useEffect(() => {
    if (!channelId) return;

    const loadChannelData = async () => {
      try {
        setLoading(true);
        console.log('Loading channel data for ID:', channelId);

        // Load channel details
        const channelData = await fetchChannel(channelId);
        console.log('Raw channel data:', channelData);

        if (!channelData) {
          setError('Channel not found');
          setLoading(false);
          return;
        }

        // Process channel data to ensure no Neo4j objects
        const processedChannel = {
          id: String(channelData.id || ''),
          name: String(channelData.name || 'Unknown Channel'),
          description: String(channelData.description || ''),
          handle: String(channelData.handle || ''),
          imageUrl: channelData.imageUrl || '/placeholder.svg',
          bannerUrl: channelData.bannerUrl || '/placeholder-banner.svg',
          subscribersCount: Number(channelData.subscribersCount || 0),
          createdAt: channelData.createdAt ? String(channelData.createdAt) : ''
        };

        setChannel(processedChannel);
        setSubscriberCount(processedChannel.subscribersCount);

        // Check subscription status if user is authenticated
        if (isAuthenticated()) {
          const subscriptionStatus = await checkSubscription(channelId);
          setIsSubscribed(!!subscriptionStatus.subscribed);
        }

        // Load channel events (only published, live, ended)
        try {
          const eventsData = await fetchPublicChannelEvents(channelId);
          console.log('Raw events data:', eventsData);

          // Safely process events to avoid Neo4j objects
          const eventsList = eventsData.events || [];
          const processedEvents = eventsList.map(event => {
            // Convert each field explicitly
            return {
              id: String(event.id || ''),
              title: String(event.title || 'Untitled Event'),
              description: String(event.description || ''),
              status: String(event.status || ''),
              startTime: event.startTime ? String(event.startTime) : null,
              endTime: event.endTime ? String(event.endTime) : null,
              imageUrl: event.imageUrl || '/placeholder.svg',
              views: Number(event.views || 0)
            };
          });

          setEvents(processedEvents);
        } catch (eventErr) {
          console.error('Failed to load events:', eventErr);
          // Don't fail the whole page if events fail
        }
      } catch (err) {
        console.error('Failed to load channel data:', err);
        setError('Failed to load channel data');
        toast({
          title: "Error",
          description: "Failed to load channel data",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    loadChannelData();
  }, [channelId]);

  const handleSubscribe = async () => {
    if (!isAuthenticated()) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to subscribe to this channel",
        variant: "default"
      });
      router.push('/login');
      return;
    }

    if (!channelId) {
      toast({
        title: "Error",
        description: "Channel ID is missing",
        variant: "destructive"
      });
      return;
    }

    try {
      // Optimistic update
      setIsSubscribed(!isSubscribed);
      setSubscriberCount(prev => isSubscribed ? prev - 1 : prev + 1);

      const result = isSubscribed
        ? await unsubscribeFromChannel(channelId)
        : await subscribeToChannel(channelId);

      // Update state based on actual result
      setIsSubscribed(!!result.subscribed);
      setSubscriberCount(Number(result.subscriberCount || 0));

      toast({
        title: result.subscribed ? "Subscribed" : "Unsubscribed",
        description: result.subscribed
          ? `You are now subscribed to ${channel?.name}`
          : `You have unsubscribed from ${channel?.name}`,
      });
    } catch (error) {
      console.error('Failed to update subscription:', error);
      // Revert optimistic update
      setIsSubscribed(!isSubscribed);
      setSubscriberCount(prev => isSubscribed ? prev + 1 : prev - 1);
      toast({
        title: "Error",
        description: "Failed to update subscription. Please try again.",
        variant: "destructive"
      });
    }
  };

  const getFilteredEvents = () => {
    if (!events.length) return [];

    const now = new Date();

    switch (activeTab) {
      case "live":
        return events.filter(event =>
          String(event.status).toLowerCase() === 'live'
        );
      case "upcoming":
        return events.filter(event => {
          const eventDate = event.startTime ? new Date(event.startTime) : null;
          return eventDate && eventDate > now && String(event.status).toLowerCase() === 'published';
        }).sort((a, b) => {
          const dateA = a.startTime ? new Date(a.startTime).getTime() : 0;
          const dateB = b.startTime ? new Date(b.startTime).getTime() : 0;
          return dateA - dateB; // Sort by closest upcoming date
        });
      case "past":
        return events.filter(event => {
          const eventDate = event.startTime ? new Date(event.startTime) : null;
          return eventDate && eventDate <= now && String(event.status).toLowerCase() === 'ended';
        }).sort((a, b) => {
          const dateA = a.startTime ? new Date(a.startTime).getTime() : 0;
          const dateB = b.startTime ? new Date(b.startTime).getTime() : 0;
          return dateB - dateA; // Sort by most recent
        });
      default:
        // For "all" tab, prioritize live events, then upcoming, then past
        const liveEvents = events.filter(event =>
          String(event.status).toLowerCase() === 'live'
        );
        const upcomingEvents = events.filter(event => {
          const eventDate = event.startTime ? new Date(event.startTime) : null;
          return eventDate && eventDate > now && String(event.status).toLowerCase() === 'published';
        }).sort((a, b) => {
          const dateA = a.startTime ? new Date(a.startTime).getTime() : 0;
          const dateB = b.startTime ? new Date(b.startTime).getTime() : 0;
          return dateA - dateB;
        });
        const pastEvents = events.filter(event => {
          const eventDate = event.startTime ? new Date(event.startTime) : null;
          return eventDate && eventDate <= now && String(event.status).toLowerCase() === 'ended';
        }).sort((a, b) => {
          const dateA = a.startTime ? new Date(a.startTime).getTime() : 0;
          const dateB = b.startTime ? new Date(b.startTime).getTime() : 0;
          return dateB - dateA;
        });

        return [...liveEvents, ...upcomingEvents, ...pastEvents];
    }
  };

  if (loading) {
    return (
      <div className="container py-8">
        <div className="flex justify-center items-center h-60">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error || !channel) {
    return (
      <div className="container py-8">
        <div className="text-center py-12 border rounded-lg">
          <h2 className="text-2xl font-bold mb-4">Channel not found</h2>
          <p className="text-red-500 mb-4">{error}</p>
          <Button onClick={() => router.push('/home')}>Back to Home</Button>
        </div>
      </div>
    );
  }

  const filteredEvents = getFilteredEvents();
  const bannerUrl = channel.bannerUrl || '/placeholder-banner.svg';
  const thumbnailUrl = channel.imageUrl || '/placeholder.svg';
  const channelName = String(channel.name || '');
  const channelHandle = String(channel.handle || '');
  const channelDescription = String(channel.description || '');

  return (
    <div className="flex flex-col min-h-screen">
      {/* Channel Banner */}
      <div className="relative w-full h-48 md:h-64 bg-muted">
        <img
          src={bannerUrl}
          alt={`${channelName} banner`}
          className="w-full h-full object-cover"
        />
      </div>

      {/* Channel Info */}
      <div className="container py-6">
        <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
          <div className="relative -mt-16 md:-mt-20">
            <Avatar className="h-24 w-24 md:h-32 md:w-32 border-4 border-background">
              <AvatarImage src={thumbnailUrl} alt={channelName} />
              <AvatarFallback className="text-2xl">{channelName.charAt(0)}</AvatarFallback>
            </Avatar>
          </div>

          <div className="flex-1">
            <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-4">
              <h1 className="text-2xl md:text-3xl font-bold">{channelName}</h1>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Users className="h-4 w-4" />
                <span>{subscriberCount} subscribers</span>
              </div>
            </div>
            <p className="text-muted-foreground mt-1">{channelHandle}</p>
          </div>

          <Button
            variant={isSubscribed ? "default" : "outline"}
            className="flex items-center gap-2"
            onClick={handleSubscribe}
          >
            <Bell className={`h-4 w-4 ${isSubscribed ? "fill-white" : ""}`} />
            {isSubscribed ? "Subscribed" : "Subscribe"}
          </Button>
        </div>

        <div className="mt-6">
          <p className="text-sm md:text-base">{channelDescription}</p>
        </div>

        <Separator className="my-6" />

        {/* Channel Events */}
        <div>
          <Tabs defaultValue="all" onValueChange={(value) => setActiveTab(value)}>
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold">Events</h2>
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="live">Live</TabsTrigger>
                <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
                <TabsTrigger value="past">Past</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="all" className="mt-0">
              {renderEventsList(filteredEvents)}
            </TabsContent>
            <TabsContent value="live" className="mt-0">
              {renderEventsList(filteredEvents)}
            </TabsContent>
            <TabsContent value="upcoming" className="mt-0">
              {renderEventsList(filteredEvents)}
            </TabsContent>
            <TabsContent value="past" className="mt-0">
              {renderEventsList(filteredEvents)}
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );

  function renderEventsList(events: any[]) {
    if (events.length === 0) {
      return (
        <div className="text-center py-12 border rounded-lg">
          <Calendar className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-medium mb-2">No events found</h3>
          <p className="text-muted-foreground">
            {activeTab === "live"
              ? "There are no live events at the moment."
              : activeTab === "upcoming"
                ? "There are no upcoming events scheduled."
                : activeTab === "past"
                  ? "There are no past events to show."
                  : "This channel hasn't published any events yet."}
          </p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {events.map((event) => (
          <div
            key={event.id}
            className="rounded-lg border overflow-hidden bg-card hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => router.push(`/event/watch/${event.id}`)}
          >
            <div className="aspect-video relative">
              <img
                src={event.imageUrl || "/placeholder.svg"}
                alt={event.title}
                className="w-full h-full object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = "/placeholder.svg";
                }}
              />
              {event.status?.toLowerCase() === 'live' && (
                <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
                  LIVE
                </div>
              )}
            </div>
            <div className="p-3">
              <h3 className="font-medium line-clamp-2 mb-1">
                {String(event.title || 'Untitled Event')}
              </h3>
              <p className="text-sm text-muted-foreground line-clamp-1 mb-1">
                {event.startTime ? new Date(event.startTime).toLocaleString() : 'TBD'}
              </p>
              <p className="text-xs text-muted-foreground">
                {Number(event.views || 0)} views
              </p>
            </div>
          </div>
        ))}
      </div>
    );
  }
}