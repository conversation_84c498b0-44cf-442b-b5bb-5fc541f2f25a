
import { useTranslation } from "react-i18next";

const Index = () => {
  const { t } = useTranslation();
  
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-muted">
      <div className="text-center max-w-2xl px-4">
        <h1 className="text-4xl font-bold mb-6">Event Horizon</h1>
        <p className="text-xl text-muted-foreground mb-8">
          {t("welcomeMessage", "Welcome to Event Horizon - your platform for streaming live events")}
        </p>
        <div className="space-y-4">
          <p className="text-muted-foreground">
            Please login or register to access the full platform features.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Index;
