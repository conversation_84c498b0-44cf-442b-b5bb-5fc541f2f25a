"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { EventCard } from "@/components/events/EventCard";
import { getUserLikedEvents, toggleLike } from "@/services/like-service";
import { Event } from "@/interfaces/types";
import { isAuthenticated } from "@/lib/auth";
import { useToast } from "@/hooks/use-toast";

export default function LikedEventsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(false);
  const [offset, setOffset] = useState(0);
  const [total, setTotal] = useState(0);

  useEffect(() => {
    // Check if user is authenticated
    if (!isAuthenticated()) {
      toast({
        title: "Authentication Required",
        description: "Please log in to view your liked events",
        variant: "default",
      });
      router.push('/login');
      return;
    }

    loadEvents();
  }, [router, toast]);

  const loadEvents = async (reset = true) => {
    try {
      setLoading(true);
      const currentOffset = reset ? 0 : offset;
      const limit = 20;

      const result = await getUserLikedEvents(limit, currentOffset);

      if (reset) {
        setEvents(result.events);
      } else {
        setEvents([...events, ...result.events]);
      }

      setTotal(result.total);
      setHasMore(result.events.length === limit && currentOffset + result.events.length < result.total);

      if (reset) {
        setOffset(result.events.length);
      } else {
        setOffset(offset + result.events.length);
      }
    } catch (error) {
      console.error('Failed to load liked events:', error);
      setError('Failed to load liked events. Please try again later.');
      toast({
        title: "Error",
        description: "Failed to load liked events. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleLoadMore = () => {
    if (hasMore && !loading) {
      loadEvents(false);
    }
  };

  const handleLike = async (eventId: string) => {
    // Check if user is authenticated
    if (!isAuthenticated()) {
      toast({
        title: "Authentication Required",
        description: "Please log in to like events",
        variant: "default",
      });
      router.push('/login');
      return;
    }

    try {
      // Find the event in the list
      const eventIndex = events.findIndex(e => e.id === eventId);
      if (eventIndex === -1) return;

      // Optimistically update UI
      const updatedEvents = [...events];
      updatedEvents[eventIndex] = {
        ...updatedEvents[eventIndex],
        isLiked: !updatedEvents[eventIndex].isLiked,
      };
      setEvents(updatedEvents);

      // Call API to toggle like
      await toggleLike(eventId);

      // Remove the event from the list since it's unliked
      setEvents(events.filter(e => e.id !== eventId));
      setTotal(total - 1);

      toast({
        title: "Unliked",
        description: "Event removed from your liked events",
      });
    } catch (error) {
      console.error('Failed to toggle like:', error);
      // Revert optimistic update by reloading events
      loadEvents();
      toast({
        title: "Error",
        description: "Failed to update like status. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (loading && events.length === 0) {
    return (
      <div className="container py-8">
        <h1 className="text-2xl font-bold mb-6">Liked Events</h1>
        <div className="flex justify-center items-center h-60">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <h1 className="text-2xl font-bold mb-6">Liked Events</h1>

      {error && (
        <div className="bg-destructive/10 text-destructive p-4 rounded-md mb-6">
          {error}
        </div>
      )}

      {events.length === 0 ? (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium mb-2">No liked events yet</h3>
          <p className="text-muted-foreground mb-4">Like some events to see them here</p>
          <Button onClick={() => router.push('/home')}>
            Explore Events
          </Button>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {events.map((event) => (
              <EventCard
                key={event.id}
                event={event}
                isLiked={true}
                onLike={() => handleLike(event.id)}
                onWatch={() => router.push(`/event/watch/${event.id}`)}
              />
            ))}
          </div>

          {hasMore && (
            <div className="flex justify-center mt-8">
              <Button
                onClick={handleLoadMore}
                disabled={loading}
                variant="outline"
              >
                {loading ? "Loading..." : "Load More"}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
