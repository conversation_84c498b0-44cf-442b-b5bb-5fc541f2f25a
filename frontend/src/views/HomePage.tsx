
"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAppContext } from "@/context/NextAppContext";
import { EventCard } from "@/components/events/EventCard";
import { Button } from "@/components/ui/button";
import { Heading } from "@/components/ui/heading";
import { Separator } from "@/components/ui/separator";
import {
  HomeIcon,
  Heart,
  History,
  ListVideo,
  UserCircle,
  LayoutGrid,
  Music,
  Gamepad2
} from "lucide-react";
import { ChatOverlay } from "@/components/chat/ChatOverlay";
import { toggleLike } from "@/services/like-service";
import { incrementViewCount } from "@/services/view-service";
import { getUserSubscribedChannels } from "@/services/subscription-service";
import { fetchCategories } from "@/services/category-service";
import { convertNeo4jIntegers } from "@/utils/neo4j-utils";
import { Channel, Category } from "@/interfaces/types";

export default function HomePage() {
  const router = useRouter();
  const { user, events: rawEvents, refreshData } = useAppContext();

  const [selectedCategory, setSelectedCategory] = useState("All");
  const [watchedEvents, setWatchedEvents] = useState<string[]>([]);
  const [likedEvents, setLikedEvents] = useState<string[]>([]);
  const [subscribedChannels, setSubscribedChannels] = useState<Channel[]>([]);
  const [activeSection, setActiveSection] = useState<'all' | 'history' | 'liked'>('all');
  const [categories, setCategories] = useState<Category[]>([]);
  const [categoriesLoading, setCategoriesLoading] = useState(true);

  // Store the converted events in a ref to avoid recalculating on every render
  const eventsRef = React.useRef(rawEvents.map(event => convertNeo4jIntegers(event)));

  // Create a ref to track previous liked events
  const prevLikedEventsRef = React.useRef<string[]>([]);

  // Update the ref when rawEvents changes
  useEffect(() => {
    console.log('Raw events changed, updating eventsRef');

    // Log the raw events to see if they have isLiked property
    rawEvents.forEach(event => {
      console.log(`Raw event ${event.id} (${event.title}) - isLiked: ${event.isLiked}`);
    });

    eventsRef.current = rawEvents.map(event => convertNeo4jIntegers(event));

    // When events are updated, also update the liked events state
    const likedEventIds = eventsRef.current
      .filter(event => event.isLiked === true)
      .map(event => event.id);

    console.log('Updated liked events from rawEvents:', likedEventIds);
    console.log('Previous liked events:', prevLikedEventsRef.current);

    // Only update if the liked events have changed from the previous value
    if (JSON.stringify(likedEventIds) !== JSON.stringify(prevLikedEventsRef.current)) {
      console.log('Liked events changed, updating state');
      prevLikedEventsRef.current = likedEventIds;
      setLikedEvents(likedEventIds);
    }

    // If no liked events were found in the raw events, but we have liked events in local storage,
    // try to restore them from local storage
    if (likedEventIds.length === 0) {
      try {
        const storedLikedEvents = localStorage.getItem('likedEvents');
        if (storedLikedEvents) {
          const parsedLikedEvents = JSON.parse(storedLikedEvents);
          console.log('Restored liked events from localStorage:', parsedLikedEvents);

          if (parsedLikedEvents.length > 0 &&
              JSON.stringify(parsedLikedEvents) !== JSON.stringify(prevLikedEventsRef.current)) {
            prevLikedEventsRef.current = parsedLikedEvents;
            setLikedEvents(parsedLikedEvents);
          }
        }
      } catch (error) {
        console.error('Error restoring liked events from localStorage:', error);
      }
    }
  }, [rawEvents]);

  // Use the ref value for rendering
  const events = eventsRef.current;

  // Create a ref to track if we've already refreshed data
  const hasRefreshedRef = React.useRef(false);

  // Add a useEffect to handle user authentication and initialize watched events
  useEffect(() => {
    if (!user) {
      router.push('/login');
    } else {
      // Initialize watched events from localStorage
      try {
        const storedWatchedEvents = localStorage.getItem('watchedEvents');
        if (storedWatchedEvents) {
          const parsedWatchedEvents = JSON.parse(storedWatchedEvents);
          console.log('Initializing watched events from localStorage:', parsedWatchedEvents);
          setWatchedEvents(parsedWatchedEvents);
        }
      } catch (error) {
        console.error('Error initializing watched events from localStorage:', error);
      }

      // Fetch subscribed channels
      const fetchSubscribedChannels = async () => {
        try {
          const channels = await getUserSubscribedChannels();
          console.log('Fetched subscribed channels:', channels);
          setSubscribedChannels(channels);
        } catch (error) {
          console.error('Error fetching subscribed channels:', error);
        }
      };

      fetchSubscribedChannels();
    }
  }, [user, router]);

  // This effect runs once when the component mounts to load data if needed
  useEffect(() => {
    // Create a variable to track if this component is still mounted
    let isMounted = true;

    const loadInitialData = async () => {
      // Only refresh if there are no events and we haven't refreshed yet
      if (rawEvents.length === 0 && !hasRefreshedRef.current) {
        console.log('Loading initial data...');
        hasRefreshedRef.current = true;

        try {
          await refreshData();
          if (isMounted) {
            console.log('Initial data load completed');
          }
        } catch (error) {
          console.error('Error loading initial data:', error);
        }
      } else {
        console.log('Component mounted with existing events:', rawEvents.length);
      }
    };

    loadInitialData();

    // Cleanup function that runs when component unmounts
    return () => {
      isMounted = false;
    };
  }, [rawEvents.length, refreshData]);

  // Real-time status updates - refresh every minute to update relative times
  useEffect(() => {
    const interval = setInterval(() => {
      // Force a re-render to update relative times like "ends in 5 minutes"
      // This is a simple way to keep the status messages current
      eventsRef.current = rawEvents.map(event => convertNeo4jIntegers(event));
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [rawEvents]);

  // Fetch categories from backend
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setCategoriesLoading(true);
        const backendCategories = await fetchCategories();

        // Map backend categories to frontend format and add "All" category
        const mappedCategories: Category[] = [
          { id: 'all', name: 'All', thumbnail: '' }, // Special "All" category
          ...backendCategories.map(cat => ({
            id: cat.id,
            name: cat.name,
            thumbnail: cat.imageUrl || 'https://placehold.co/300x200',
            imageUrl: cat.imageUrl
          }))
        ];

        setCategories(mappedCategories);
      } catch (error) {
        console.error('Failed to load categories:', error);
        // Fallback to hardcoded categories if API fails
        setCategories([
          { id: 'all', name: 'All', thumbnail: '' },
          { id: 'sport', name: 'Sport', thumbnail: '' },
          { id: 'music', name: 'Music', thumbnail: '' },
          { id: 'gaming', name: 'Gaming', thumbnail: '' }
        ]);
      } finally {
        setCategoriesLoading(false);
      }
    };

    loadCategories();
  }, []);

  if (!user) return null;

  // Log all events and their statuses for debugging
  console.log('All events in HomePage:', events);

  // Check for published events with case-insensitive comparison
  // Use eventsRef.current to ensure we're using the latest converted events
  const publishedEvents = eventsRef.current.filter(e => {
    if (!e.status) return false;
    return String(e.status).toLowerCase() === 'published';
  });

  console.log('Published events count:', publishedEvents.length);
  publishedEvents.forEach(e => console.log(`Published event: ${e.id} - ${e.title} - Status: ${e.status}`));

  // Log all event statuses to see what we have
  console.log('All event statuses:');
  events.forEach(e => console.log(`Event ${e.id} - ${e.title} - Status: ${e.status}`));

  const getFilteredEvents = () => {
    // Use eventsRef.current to ensure we're using the latest converted events
    const currentEvents = eventsRef.current;

    // Log all events to see what we're working with
    console.log('All events:', currentEvents);

    // Recalculate upcoming and past events using the current events
    const currentUpcomingEvents = currentEvents
      .filter(e => {
        const eventDate = e.startTime ? new Date(e.startTime) : e.date ? new Date(e.date) : null;
        return eventDate && eventDate > new Date();
      })
      .sort(() => Math.random() - 0.5);

    const currentPastEvents = currentEvents
      .filter(e => {
        const eventDate = e.startTime ? new Date(e.startTime) : e.date ? new Date(e.date) : null;
        return eventDate && eventDate <= new Date();
      })
      .sort(() => Math.random() - 0.5);

    // Create a combined list of all events
    const allEvents = [...currentUpcomingEvents, ...currentPastEvents];
    console.log('Combined upcoming and past events:', allEvents);

    // First filter to only show published, live, or ended events (not draft)
    // Make sure to handle both string and undefined status values
    let filteredList = allEvents.filter(event => {
      // Default to showing the event if status is missing (for backward compatibility)
      if (!event.status) {
        console.log(`Event ${event.id} (${event.title}) - Status is missing, showing by default`);
        return true;
      }

      // Convert status to string to ensure consistent comparison
      const status = String(event.status).toLowerCase();

      // Only show published, live, or ended events (not draft or upcoming)
      const isVisible = status === 'published' || status === 'live' || status === 'ended';

      console.log(`Event ${event.id} (${event.title}) - Status: ${status}, Original Status: ${event.status}, Visible: ${isVisible}`);

      return isVisible;
    });

    // Log the filtered list
    console.log('Events after status filtering:', filteredList.length);
    filteredList.forEach(event => {
      console.log(`Filtered event: ${event.id} - ${event.title} - Status: ${event.status}`);
    });

    console.log('After status filter:', filteredList);

    if (selectedCategory !== "All") {
      filteredList = filteredList.filter(event => {
        const matchesCategory = event.category === selectedCategory ||
                               (event.tags && event.tags.includes(selectedCategory));
        console.log(`Event ${event.id} - Category match: ${matchesCategory}`);
        return matchesCategory;
      });
      console.log('After category filter:', filteredList);
    }

    let result;
    switch (activeSection) {
      case 'history':
        result = filteredList.filter(event => watchedEvents.includes(event.id));
        console.log('History section events:', result);
        return result;
      case 'liked':
        result = filteredList.filter(event => likedEvents.includes(event.id));
        console.log('Liked section events:', result);
        return result;
      default:
        console.log('Final filtered events:', filteredList);
        return filteredList;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch(category) {
      case "Sport":
        return <LayoutGrid className="h-4 w-4" />; // Changed from Basketball to LayoutGrid
      case "Music":
        return <Music className="h-4 w-4" />;
      case "Gaming":
        return <Gamepad2 className="h-4 w-4" />; // Changed from Gamepad to Gamepad2
      default:
        return <LayoutGrid className="h-4 w-4" />;
    }
  };

  const handleWatchEvent = async (eventId: string) => {
    if (!watchedEvents.includes(eventId)) {
      setWatchedEvents([...watchedEvents, eventId]);

      // Store in localStorage to track watched events
      try {
        const storedWatchedEvents = localStorage.getItem('watchedEvents');
        const parsedWatchedEvents = storedWatchedEvents ? JSON.parse(storedWatchedEvents) : [];

        if (!parsedWatchedEvents.includes(eventId)) {
          parsedWatchedEvents.push(eventId);
          localStorage.setItem('watchedEvents', JSON.stringify(parsedWatchedEvents));
        }
      } catch (error) {
        console.error('Failed to store watched event in localStorage:', error);
      }

      // Note: We're removing the view count increment from here
      // Views will only be counted on the WatchEventPage
    }
  };

  const handleLikeEvent = async (eventId: string) => {
    try {
      console.log(`Handling like event for ${eventId}. Current liked events:`, likedEvents);

      // Store current state for potential reversion
      const currentLikedEvents = [...likedEvents];
      const isCurrentlyLiked = currentLikedEvents.includes(eventId);

      // Find the event in the current events
      const eventToUpdate = eventsRef.current.find(event => event.id === eventId);
      if (eventToUpdate) {
        console.log(`Event before like toggle: ${eventToUpdate.id} (${eventToUpdate.title}) - isLiked: ${eventToUpdate.isLiked}`);
      }

      // Optimistically update UI (only once)
      if (isCurrentlyLiked) {
        // Remove from liked events
        setLikedEvents(currentLikedEvents.filter(id => id !== eventId));
      } else {
        // Add to liked events
        setLikedEvents([...currentLikedEvents, eventId]);
      }

      // Toggle like in the backend
      const response = await toggleLike(eventId);
      console.log('Like response:', response);

      // Update the isLiked property and likes count in the events ref to ensure it persists
      eventsRef.current = eventsRef.current.map(event => {
        if (event.id === eventId) {
          // Use the count from the response if available
          const newLikes = response.count !== undefined ? response.count : (event.likes || 0);

          const updatedEvent = {
            ...event,
            isLiked: response.liked,
            likes: newLikes
          };
          console.log(`Updated event in ref: ${updatedEvent.id} - isLiked: ${updatedEvent.isLiked}, likes: ${updatedEvent.likes}`);
          return updatedEvent;
        }
        return event;
      });

      // No need to update state again if the response matches our optimistic update
      // Only update if there's a mismatch between our optimistic update and the actual result
      if ((response.liked && !isCurrentlyLiked) || (!response.liked && isCurrentlyLiked)) {
        // The backend response matches our optimistic update, no need to change state
        console.log('Backend response matches optimistic update');
      } else {
        // The backend response doesn't match our optimistic update, revert to the correct state
        console.log('Backend response does not match optimistic update, reverting state');
        if (response.liked) {
          setLikedEvents([...currentLikedEvents, eventId]);
        } else {
          setLikedEvents(currentLikedEvents.filter(id => id !== eventId));
        }
      }

      // Store the updated liked events in localStorage
      try {
        const updatedLikedEvents = response.liked
          ? [...currentLikedEvents.filter(id => id !== eventId), eventId]
          : currentLikedEvents.filter(id => id !== eventId);

        localStorage.setItem('likedEvents', JSON.stringify(updatedLikedEvents));
        console.log('Stored liked events in localStorage:', updatedLikedEvents);
      } catch (error) {
        console.error('Error storing liked events in localStorage:', error);
      }

      // Refresh data to ensure the liked status is updated in the context
      // This is important for when the page is refreshed
      console.log('Refreshing data to update liked status in context');
      await refreshData();

      // Log the updated events after refresh
      console.log('Events after refresh:');
      eventsRef.current.forEach(event => {
        if (event.id === eventId) {
          console.log(`Event after refresh: ${event.id} (${event.title}) - isLiked: ${event.isLiked}`);
        }
      });
    } catch (error) {
      console.error('Failed to toggle like:', error);
    }
  };

  return (
    <div className="flex">
      <div className="hidden md:flex flex-col w-64 p-4 bg-sidebar-background border-r gap-2 min-h-[calc(100vh-4rem)]">
        <div className="flex flex-col gap-1">
          <Button
            variant="ghost"
            className="justify-start gap-3 font-medium"
            onClick={() => {
              setActiveSection('all');
              router.push('/home');
            }}
          >
            <HomeIcon className="h-5 w-5" />
            <span>Home</span>
          </Button>
        </div>

        <Separator className="my-3" />

        <div className="flex flex-col gap-1">
          <h3 className="px-3 mb-1 font-medium">You</h3>
          <Button
            variant="ghost"
            className="justify-start gap-3"
            onClick={() => router.push('/account/channels')}
          >
            <UserCircle className="h-5 w-5" />
            <span>Your channels</span>
          </Button>

          <Button
            variant="ghost"
            className="justify-start gap-3"
            onClick={() => router.push('/account/purchases')}
          >
            <ListVideo className="h-5 w-5" />
            <span>Your purchases</span>
          </Button>

          <Button
            variant={activeSection === 'history' ? 'secondary' : 'ghost'}
            className="justify-start gap-3"
            onClick={() => setActiveSection('history')}
          >
            <History className="h-5 w-5" />
            <span>History</span>
          </Button>

          <Button
            variant="ghost"
            className="justify-start gap-3"
            onClick={() => router.push('/liked-events')}
          >
            <Heart className="h-5 w-5" />
            <span>Liked videos</span>
          </Button>
        </div>

        <Separator className="my-3" />

        <div className="flex flex-col gap-1">
          <h3 className="px-3 mb-1 font-medium">Subscriptions</h3>
          {subscribedChannels.length === 0 ? (
            <div className="text-sm text-muted-foreground px-3 py-2">
              You haven't subscribed to any channels yet
            </div>
          ) : (
            <div className="flex flex-col gap-1 max-h-60 overflow-y-auto">
              {subscribedChannels.map(channel => (
                <Button
                  key={channel.id}
                  variant="ghost"
                  className="justify-start gap-3 text-sm"
                  onClick={() => router.push(`/channel-view/${channel.id}`)}
                >
                  <div className="w-6 h-6 rounded-full overflow-hidden bg-muted flex-shrink-0">
                    {channel.imageUrl ? (
                      <img
                        src={channel.imageUrl}
                        alt={channel.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <ListVideo className="h-4 w-4 m-1" />
                    )}
                  </div>
                  <span className="truncate">{channel.name}</span>
                </Button>
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="flex-1 min-h-[calc(100vh-4rem)]">
        <div className="sticky top-16 z-10 bg-background pt-4 px-4 md:px-8 pb-4 shadow-sm border-b">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <LayoutGrid className="h-5 w-5 text-muted-foreground" />
              <h2 className="font-semibold">
                {activeSection === 'history' ? 'Watch History' :
                 activeSection === 'liked' ? 'Liked Videos' :
                 'Categories'}
              </h2>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                console.log('Manual refresh requested');

                // Prevent multiple refreshes
                if (hasRefreshedRef.current) {
                  console.log('Refresh already in progress, ignoring request');
                  return;
                }

                // Set flag immediately
                hasRefreshedRef.current = true;

                // Function to safely reset the flag
                const resetFlag = () => {
                  console.log('Resetting refresh flag');
                  hasRefreshedRef.current = false;
                };

                // Safety timeout in case the operation hangs
                const safetyTimeout = setTimeout(() => {
                  console.warn('Safety timeout reached - refresh may have stalled');
                  resetFlag();
                }, 15000); // 15 second safety timeout

                // Perform the refresh
                console.log('Starting refresh operation');
                refreshData()
                  .then(() => {
                    console.log('Refresh completed successfully');
                  })
                  .catch((error) => {
                    console.error('Error during refresh:', error);
                  })
                  .finally(() => {
                    // Clear safety timeout
                    clearTimeout(safetyTimeout);

                    // Wait before allowing new refreshes
                    setTimeout(resetFlag, 5000);
                  });
              }}
            >
              Refresh
            </Button>
          </div>

          {activeSection === 'all' && (
            <div className="flex gap-2 overflow-x-auto pb-2 scrollbar-hide">
              {categoriesLoading ? (
                <div className="flex gap-2">
                  {[1, 2, 3, 4].map((i) => (
                    <div
                      key={i}
                      className="h-8 w-20 bg-muted rounded-full animate-pulse flex-shrink-0"
                    />
                  ))}
                </div>
              ) : (
                categories.map((category) => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.name ? "default" : "secondary"}
                    size="sm"
                    onClick={() => setSelectedCategory(category.name)}
                    className="rounded-full whitespace-nowrap flex-shrink-0"
                  >
                    {category.name !== "All" && getCategoryIcon(category.name)}
                    <span>{category.name}</span>
                  </Button>
                ))
              )}
            </div>
          )}
        </div>

        <div className="p-4 md:p-8">
          {events.length === 0 ? (
            <div className="text-center py-12">
              <h3 className="text-lg font-medium mb-2">No events yet</h3>
              <p className="text-muted-foreground mb-4">Create your first event or channel to get started</p>
              <div className="flex justify-center gap-4">
                <Button onClick={() => router.push('/channel-create')} variant="outline">
                  Create Channel
                </Button>
                <Button onClick={() => router.push('/event-create')}>
                  Create Event
                </Button>
              </div>
            </div>
          ) : (
            <>
              {/* Get the filtered events */}
              {(() => {
                const filteredEvents = getFilteredEvents();
                console.log('Filtered events to display:', filteredEvents);

                return filteredEvents.length === 0 ? (
                  <div className="text-center py-12">
                    <h3 className="text-lg font-medium mb-2">No published events found</h3>
                    <p className="text-muted-foreground mb-4">
                      There are {events.length} events in total, but none match the current filters.
                    </p>
                    <div className="flex justify-center gap-4">
                      <Button onClick={() => router.push('/event-create')}>
                        Create Event
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {filteredEvents.map((event) => (
                      <EventCard
                        key={event.id}
                        event={event}
                        hideSubscribeButton
                        onWatch={() => handleWatchEvent(event.id)}
                        onLike={() => handleLikeEvent(event.id)}
                        isLiked={likedEvents.includes(event.id)}
                      />
                    ))}
                  </div>
                );
              })()}
            </>
          )}
        </div>
      </div>

      <ChatOverlay />
    </div>
  );
}
