"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Heading } from "@/components/ui/heading";
import { ArrowLeft } from "lucide-react";
import CreateEventDialog from "@/components/events/CreateEventDialog";
import { isAuthenticated } from "@/lib/auth";
import { fetchChannel } from "@/services/channel-service";
import { Channel } from "@/interfaces/types";

export default function CreateEventPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [channel, setChannel] = useState<Channel | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const channelId = searchParams?.get('channelId') || '';

  // Load channel data and check authentication
  useEffect(() => {
    const init = async () => {
      try {
        setLoading(true);
        
        // Check authentication
        if (!isAuthenticated()) {
          router.push("/login");
          return;
        }
        
        // If channelId is provided, load channel data
        if (channelId) {
          try {
            const channelData = await fetchChannel(channelId);
            setChannel(channelData);
            
            // Check if channel is approved
            if (channelData.status !== 'active') {
              setError("This channel is not approved yet. Events can only be created for approved channels.");
            }
          } catch (err) {
            console.error('Failed to load channel data', err);
            setError("Channel not found or you don't have permission to create events for this channel.");
          }
        } else {
          // No channelId provided
          setError("Please select a channel to create an event.");
        }
      } catch (err) {
        console.error('Initialization error', err);
      } finally {
        setLoading(false);
      }
    };

    init();
  }, [channelId, router]);

  const handleOpenDialog = () => {
    if (channel && channel.status === 'active') {
      setIsDialogOpen(true);
    } else {
      // Don't open dialog if channel is not approved
      if (channel) {
        alert("This channel is not approved yet. Events can only be created for approved channels.");
      } else {
        alert("Please select a channel to create an event.");
      }
    }
  };
  
  const handleCloseDialog = () => setIsDialogOpen(false);

  if (loading) {
    return (
      <div className="container py-8">
        <div className="flex justify-center items-center h-60">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <Button 
        variant="ghost" 
        className="mb-6 flex items-center gap-2"
        onClick={() => router.push('/account/channels')}
      >
        <ArrowLeft className="h-4 w-4" />
        Back to Channels
      </Button>
      
      <Heading 
        title="Create Event" 
        description={channel ? `Create a new event for ${channel.name}` : "Create a new event"}
        className="mb-6"
      />
      
      <div className="max-w-3xl mx-auto">
        <div className="bg-muted/50 rounded-lg p-8 text-center">
          {error ? (
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-4">Cannot Create Event</h2>
              <p className="text-muted-foreground mb-6">{error}</p>
              <Button 
                onClick={() => router.push('/account/channels')}
                className="gap-2"
              >
                Go to My Channels
              </Button>
            </div>
          ) : (
            <>
              <h2 className="text-2xl font-bold mb-4">Schedule Your Event</h2>
              <p className="text-muted-foreground mb-6">
                Create a new live event for your audience. You can set the date, time, and price for your event.
              </p>
              
              <Button size="lg" className="gap-2" onClick={handleOpenDialog}>
                Create New Event
              </Button>
            </>
          )}
          
          {channelId && (
            <CreateEventDialog 
              open={isDialogOpen}
              onClose={handleCloseDialog}
              channelId={channelId}
            />
          )}
        </div>
      </div>
    </div>
  );
}