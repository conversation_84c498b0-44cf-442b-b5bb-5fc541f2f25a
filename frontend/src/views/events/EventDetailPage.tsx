"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from "next/navigation";
import { useParams } from 'react-router-dom';
import { updateMetaTags } from "@/utils/seo";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { formatRelativeDate, formatDate, formatDateWithTimezone, formatTimezone, getBrowserTimezone } from "@/utils/date-utils";
import { neo4jIntegerToString, convertNeo4jIntegers } from "@/utils/neo4j-utils";
import {
  ThumbsUp,
  ThumbsDown,
  Share2,
  UserRound,
  MessageCircle,
  ShoppingCart,
  Send,
  DollarSign,
  Users,
  Play,
  Clock,
  Calendar
} from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Textarea } from "@/components/ui/textarea";
import { CommentSection } from "@/components/comments/CommentSection";
import { useToast } from "@/hooks/use-toast";
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ContentCreatorFinancialsView } from "@/components/events/ContentCreatorFinancialsView";
import { fetchEvent, fetchChannelEvents, setEventLive, endEvent, publishEvent, checkEventAccess } from "@/services/event-service";
import { fetchChannel } from "@/services/channel-service";
import { fetchCategories } from "@/services/category-service";
import { fetchPricingPlans } from "@/services/pricing-plan-service";
import { Event, Channel, Category, PricingPlan } from "@/interfaces/types";
import { createPaymentIntent } from "@/services/payment-service";
import { toggleLike, checkUserLiked, getLikeCount } from "@/services/like-service";
import { isAuthenticated, getCurrentUser, checkAdminStatus } from "@/lib/auth";
import { fetchEventPurchases } from "@/services/admin-service";

// Comment section is now handled by the CommentSection component

interface EventDetailPageProps {
  contentCreatorView?: boolean;
}

export default function EventDetailPage({ contentCreatorView = false }: EventDetailPageProps) {
  const router = useRouter();
  const params = useParams();
  const { toast } = useToast();

  // Get the ID from URL parameters
  const id = params.id as string;
  const [event, setEvent] = useState<Event | null>(null);
  const [channel, setChannel] = useState<Channel | null>(null);
  const [isLiked, setIsLiked] = useState(false);
  const [hasAccess, setHasAccess] = useState(false);

  // Log the ID to verify it's a string
  console.log('EventDetailPage received ID (type):', typeof id);
  console.log('EventDetailPage received ID (value):', id);
  // Comments are now handled by the CommentSection component
  const [activeTab, setActiveTab] = useState("overview");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [recommendations, setRecommendations] = useState<Event[]>([]);
  const [isOwner, setIsOwner] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [pricingPlans, setPricingPlans] = useState<PricingPlan[]>([]);
  const [isAdmin, setIsAdmin] = useState(false);
  const [paidUsers, setPaidUsers] = useState<any[]>([]);
  const [paidUsersLoading, setPaidUsersLoading] = useState(false);
  const [paidUsersPage, setPaidUsersPage] = useState(1);
  const [paidUsersTotal, setPaidUsersTotal] = useState(0);

  // Load event data
  useEffect(() => {
    console.log('EventDetailPage - Event ID:', id);

    const loadEventData = async () => {
      try {
        setLoading(true);

        if (!id) {
          console.error('No event ID provided');
          setError('No event ID provided');
          setLoading(false);
          return;
        }

        // The ID should already be a string at this point
        // But let's ensure it's a string just to be safe
        const eventId = String(id);
        console.log('Using event ID:', eventId);

        // Load event details
        console.log('Fetching event with ID:', eventId);
        const eventData = await fetchEvent(eventId);
        console.log('Fetched event data:', eventData);

        // Convert any Neo4j integers in the event data to regular numbers/strings
        const safeEventData = convertNeo4jIntegers(eventData);
        console.log('Converted event data:', safeEventData);
        setEvent(safeEventData);

        // Set like status from event data if available
        if (safeEventData.isLiked !== undefined) {
          setIsLiked(safeEventData.isLiked);
        }

        // Load categories and pricing plans
        const [categoriesData, pricingPlansData] = await Promise.all([
          fetchCategories(),
          fetchPricingPlans()
        ]);
        setCategories(convertNeo4jIntegers(categoriesData));
        setPricingPlans(convertNeo4jIntegers(pricingPlansData));

        // Load channel details
        if (safeEventData.channel?.id) {
          const channelData = await fetchChannel(String(safeEventData.channel.id));
          setChannel(convertNeo4jIntegers(channelData));

          // Check if user is the channel owner
          setIsOwner(contentCreatorView || channelData.status === 'active');

          // Load recommendations (other events from the same channel)
          const channelEvents = await fetchChannelEvents(String(safeEventData.channel.id));
          // Filter and convert Neo4j integers
          const safeChannelEvents = convertNeo4jIntegers(channelEvents);
          setRecommendations(safeChannelEvents.filter(e => String(e.id) !== String(id)).slice(0, 6));
        }

        // Check like status regardless of authentication
        // The checkUserLiked function will handle unauthenticated users
        try {
          // The ID should already be a string at this point
          const eventId = String(id);

          const likeStatus = await checkUserLiked(eventId);
          setIsLiked(likeStatus.liked);
        } catch (error) {
          console.error('Failed to check like status:', error);
          // Default to not liked on error
          setIsLiked(false);
        }

        // Check if user has access to this event
        if (isAuthenticated()) {
          try {
            const accessData = await checkEventAccess(id);
            setHasAccess(accessData);
          } catch (error) {
            console.error('Failed to check event access:', error);
            setHasAccess(false);
          }
        }

        // Check if the current user is an admin
        const adminStatus = await checkAdminStatus();
        setIsAdmin(adminStatus);
      } catch (err) {
        console.error('Failed to load event data', err);
        setError('Failed to load event data');
        toast({
          title: "Error",
          description: "Failed to load event data",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    loadEventData();
  }, [id, contentCreatorView, toast]);

  // Load paid users for admin
  const loadPaidUsers = async (page: number = 1) => {
    if (!isAdmin || !event?.id) return;

    try {
      setPaidUsersLoading(true);
      const result = await fetchEventPurchases(String(event.id), page, 10);
      setPaidUsers(result.purchases);
      setPaidUsersTotal(result.total);
      setPaidUsersPage(page);
    } catch (error) {
      console.error('Failed to load paid users:', error);
      toast({
        title: "Error",
        description: "Failed to load paid users",
        variant: "destructive"
      });
    } finally {
      setPaidUsersLoading(false);
    }
  };

  // Load paid users when tab changes to paid-users
  useEffect(() => {
    if (activeTab === "paid-users" && isAdmin && paidUsers.length === 0) {
      loadPaidUsers(1);
    }
  }, [activeTab, isAdmin]);

  // Update meta tags when event is loaded
  useEffect(() => {
    if (event) {
      updateMetaTags({
        title: `${event.title} | Event Horizon`,
        description: event.description || 'Join us for this exciting event!',
        image: event.imageUrl || '/placeholder.svg',
      });
    }

    return () => {
      updateMetaTags({
        title: 'Event Horizon - Livestream Platform',
        description: 'A modern platform for livestream events',
        image: '/placeholder.svg'
      });
    };
  }, [event]);

  // Comment handling is now done in the CommentSection component

  const handlePurchaseEvent = async () => {
    if (!event) return;

    try {
      // Create a payment intent for the event price
      const response = await createPaymentIntent(event.price, event.currency);

      // In a real app, you'd use Stripe Elements or Checkout to collect payment details
      // For now, just show a success message
      toast({
        title: "Success",
        description: "Payment successful! You now have access to this event.",
      });
    } catch (error) {
      console.error('Payment failed:', error);
      toast({
        title: "Error",
        description: "Payment failed. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleLiveStatus = async () => {
    if (!event) return;

    try {
      if (event.status !== 'live') {
        // Set event as live
        await setEventLive(event.id);
        setEvent({...event, status: 'live'});
        toast({
          title: "Success",
          description: "Event is now live!",
        });
      } else {
        // End the event
        await endEvent(event.id);
        setEvent({...event, status: 'ended'});
        toast({
          title: "Success",
          description: "Event has ended.",
        });
      }
    } catch (error) {
      console.error('Failed to update event status:', error);
      toast({
        title: "Error",
        description: "Failed to update event status. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handlePublishEvent = async () => {
    if (!event) return;

    try {
      // Check if all required fields are filled
      const missingFields = [];

      if (!event.title || event.title.trim() === '') {
        missingFields.push('Title');
      }

      if (!event.description || event.description.trim() === '') {
        missingFields.push('Description');
      }

      if (!event.imageUrl || event.imageUrl.trim() === '') {
        missingFields.push('Thumbnail');
      }

      if (!event.category || event.category.trim() === '') {
        missingFields.push('Category');
      }

      if (!event.pricingPlanId || event.pricingPlanId.trim() === '') {
        missingFields.push('Price Plan');
      }

      if (!event.startTime || event.startTime.trim() === '') {
        missingFields.push('Start Time');
      }

      if (!event.endTime || event.endTime.trim() === '') {
        missingFields.push('End Time');
      }

      if (!event.timezone || event.timezone.trim() === '') {
        missingFields.push('Timezone');
      }

      if (missingFields.length > 0) {
        const fieldsList = missingFields.join(', ');
        toast({
          title: "Missing Fields",
          description: `Please fill in the following required fields before publishing: ${fieldsList}`,
          variant: "destructive",
        });
        return;
      }

      // Publish the event
      const publishedEvent = await publishEvent(event.id);
      setEvent({...event, status: 'published'});
      toast({
        title: "Success",
        description: "Event published successfully! It will now appear on the home page.",
      });
    } catch (error) {
      console.error('Failed to publish event:', error);
      toast({
        title: "Error",
        description: "Failed to publish event. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleLike = async () => {
    if (!event) return;

    // Check authentication before proceeding
    if (!isAuthenticated()) {
      // Show a toast message and redirect to login
      toast({
        title: "Authentication Required",
        description: "Please log in to like events",
        variant: "default",
      });
      router.push('/login');
      return;
    }

    try {
      // Optimistically update UI
      setIsLiked(!isLiked);

      // Ensure event ID is a string
      const eventId = String(event.id);

      // Call API to toggle like
      const result = await toggleLike(eventId);

      // Update state based on actual result
      setIsLiked(result.liked);

      // Update event like count if available
      if (event.likeCount !== undefined) {
        const newLikeCount = result.liked
          ? (event.likeCount + 1)
          : Math.max(0, event.likeCount - 1);
        setEvent({...event, likeCount: newLikeCount});
      }

      toast({
        title: result.liked ? "Liked" : "Unliked",
        description: result.liked ? "Added to your liked events" : "Removed from your liked events",
      });
    } catch (error) {
      console.error('Failed to toggle like:', error);
      // Revert optimistic update
      setIsLiked(!isLiked);
      toast({
        title: "Error",
        description: "Failed to update like status. Please try again.",
        variant: "destructive"
      });
    }
  };

  if (loading) {
    return (
      <div className="container py-8">
        <div className="flex justify-center items-center h-60">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error || !event) {
    return (
      <div className="container py-8 text-center">
        <h2 className="text-2xl font-bold mb-4">Event not found</h2>
        <p className="text-red-500 mb-4">{error}</p>
        <Button onClick={() => router.push('/home')}>Back to Home</Button>
      </div>
    );
  }

  // Use the actual authentication check from lib/auth
  const views = event.views || 0;
  const likeCount = event.likeCount !== undefined ? event.likeCount : 0;
  const subscribersCount = channel?.subscribersCount || 0;
  const channelName = channel?.name || event.channel?.name || "Channel";

  if (isOwner) {
    // Ensure all Neo4j integers are converted to regular numbers/strings
    const safeEvent = event ? convertNeo4jIntegers(event) : null;
    const eventIdString = safeEvent?.id ? String(safeEvent.id) : "";

    return (
      <div className="container max-w-[1800px] py-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold mb-2">{safeEvent?.title}</h1>
          <div className="flex flex-wrap items-center gap-2 text-sm text-muted-foreground">
            <span>{views} views</span>
            <span>•</span>
            <span>{formatRelativeDate(safeEvent?.startTime)}</span>
            <span>•</span>
            <span>{likeCount} likes</span>
            <span className={`ml-2 inline-flex items-center px-2 py-1 text-xs rounded-full ${
              safeEvent?.status === 'live'
                ? 'bg-green-100 text-green-800'
                : safeEvent?.status === 'upcoming'
                ? 'bg-yellow-100 text-yellow-800'
                : safeEvent?.status === 'draft'
                ? 'bg-gray-100 text-gray-800'
                : safeEvent?.status === 'published'
                ? 'bg-blue-100 text-blue-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {safeEvent?.status === 'live' ? 'Live' :
              safeEvent?.status === 'upcoming' ? 'Upcoming' :
              safeEvent?.status === 'draft' ? 'Draft' :
              safeEvent?.status === 'published' ? 'Published' : 'Ended'}
            </span>
          </div>
        </div>

        <Tabs defaultValue="overview" className="w-full" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="comments">
              <MessageCircle className="h-4 w-4 mr-2" />
              Comments
            </TabsTrigger>
            <TabsTrigger value="financials">
              <DollarSign className="h-4 w-4 mr-2" />
              Financials
            </TabsTrigger>
            {isAdmin && (
              <TabsTrigger value="paid-users">
                <Users className="h-4 w-4 mr-2" />
                Paid Users
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <div className="relative aspect-video bg-black rounded-lg overflow-hidden mb-4">
                  <img
                    src={safeEvent?.imageUrl || '/placeholder.svg'}
                    alt={safeEvent?.title}
                    className="w-full h-full object-cover"
                  />
                </div>

                <Card className="p-4 mb-4">
                  <p className="text-sm whitespace-pre-wrap">{safeEvent?.description}</p>
                </Card>
              </div>

              <div className="space-y-4">
                <Card className="p-4">
                  <h3 className="font-medium mb-3">Event Summary</h3>
                  <div className="space-y-3">
                    {/* Start Time */}
                    {safeEvent?.startTime && (
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Start Time:</span>
                        </div>
                        {(() => {
                          const startTimeInfo = formatDateWithTimezone(safeEvent.startTime, safeEvent.timezone);
                          const browserTimezone = getBrowserTimezone();
                          const showBrowserTime = safeEvent.timezone && safeEvent.timezone !== browserTimezone;

                          return (
                            <div className="text-right space-y-1">
                              <div className="text-sm">
                                {startTimeInfo.eventTime}
                                {safeEvent.timezone && (
                                  <span className="text-muted-foreground ml-1">
                                    ({formatTimezone(safeEvent.timezone)})
                                  </span>
                                )}
                              </div>
                              {showBrowserTime && (
                                <div className="text-xs text-muted-foreground">
                                  Your time: {startTimeInfo.browserTime}
                                </div>
                              )}
                            </div>
                          );
                        })()}
                      </div>
                    )}

                    {/* End Time */}
                    {safeEvent?.endTime && (
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">End Time:</span>
                        </div>
                        {(() => {
                          const endTimeInfo = formatDateWithTimezone(safeEvent.endTime, safeEvent.timezone);
                          const browserTimezone = getBrowserTimezone();
                          const showBrowserTime = safeEvent.timezone && safeEvent.timezone !== browserTimezone;

                          return (
                            <div className="text-right space-y-1">
                              <div className="text-sm">
                                {endTimeInfo.eventTime}
                                {safeEvent.timezone && (
                                  <span className="text-muted-foreground ml-1">
                                    ({formatTimezone(safeEvent.timezone)})
                                  </span>
                                )}
                              </div>
                              {showBrowserTime && (
                                <div className="text-xs text-muted-foreground">
                                  Your time: {endTimeInfo.browserTime}
                                </div>
                              )}
                            </div>
                          );
                        })()}
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Status:</span>
                      <span className="capitalize">{safeEvent?.status}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Price:</span>
                      <span>{safeEvent?.price} {safeEvent?.currency}</span>
                    </div>
                    {safeEvent?.pricingPlanId && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Pricing Plan:</span>
                        <span>
                          {pricingPlans.find(p => String(p.id) === String(safeEvent.pricingPlanId))?.product || safeEvent.pricingPlanId}
                        </span>
                      </div>
                    )}
                    {safeEvent?.category && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Category:</span>
                        <span>{categories.find(c => String(c.id) === String(safeEvent.category))?.name || safeEvent.category}</span>
                      </div>
                    )}
                    {safeEvent?.forKids !== undefined && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">For Kids:</span>
                        <span>{safeEvent.forKids ? 'Yes' : 'No'}</span>
                      </div>
                    )}
                    {safeEvent?.viewersOverEighteen !== undefined && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">18+ Content:</span>
                        <span>{safeEvent.viewersOverEighteen ? 'Yes' : 'No'}</span>
                      </div>
                    )}
                    {safeEvent?.streamUrl && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Stream URL:</span>
                        <span className="truncate max-w-[150px]">{safeEvent.streamUrl}</span>
                      </div>
                    )}
                  </div>
                </Card>

                <div className="flex flex-col gap-2">
                  <div className="flex gap-2">
                    <Button className="flex-1" onClick={() => router.push(`/event/edit/${eventIdString}`)}>
                      Edit Event
                    </Button>
                    <Button
                      variant={safeEvent?.status !== 'live' ? "default" : "destructive"}
                      className="flex-1"
                      onClick={handleLiveStatus}
                    >
                      {safeEvent?.status !== 'live' ? "Go Live" : "End Stream"}
                    </Button>
                  </div>

                  {/* Publish button - only show if event is in draft status */}
                  {safeEvent?.status === 'draft' && (
                    <Button
                      variant="secondary"
                      className="w-full"
                      onClick={handlePublishEvent}
                    >
                      Publish Event
                    </Button>
                  )}

                  {/* Watch button - only show if event is published or live */}
                  {(safeEvent?.status === 'published' || safeEvent?.status === 'live') && (
                    <Button
                      variant="default"
                      className="w-full"
                      onClick={() => router.push(`/event/watch/${eventIdString}`)}
                    >
                      Watch Event
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="comments" className="space-y-4">
            <CommentSection eventId={eventIdString} />
          </TabsContent>

          <TabsContent value="financials">
            <ContentCreatorFinancialsView event={safeEvent} />
          </TabsContent>

          {isAdmin && (
            <TabsContent value="paid-users">
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Paid Users</h3>
                {paidUsersLoading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : paidUsers.length === 0 ? (
                  <p className="text-center text-muted-foreground py-8">No users have purchased this event yet.</p>
                ) : (
                  <div className="space-y-4">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left py-2 px-4">User</th>
                            <th className="text-left py-2 px-4">Email</th>
                            <th className="text-left py-2 px-4">Amount</th>
                            <th className="text-left py-2 px-4">Purchase Date</th>
                          </tr>
                        </thead>
                        <tbody>
                          {paidUsers.map((purchase, index) => (
                            <tr key={index} className="border-b">
                              <td className="py-2 px-4">
                                {purchase.firstName} {purchase.lastName}
                                {purchase.username && <span className="text-sm text-muted-foreground"> (@{purchase.username})</span>}
                              </td>
                              <td className="py-2 px-4">{purchase.email}</td>
                              <td className="py-2 px-4">${purchase.amount?.toFixed(2) || '0.00'}</td>
                              <td className="py-2 px-4">
                                {purchase.purchaseDate ? formatDate(purchase.purchaseDate) : 'N/A'}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>

                    {/* Pagination */}
                    {paidUsersTotal > 10 && (
                      <div className="flex justify-center gap-2 mt-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => loadPaidUsers(paidUsersPage - 1)}
                          disabled={paidUsersPage === 1}
                        >
                          Previous
                        </Button>
                        <span className="py-2 px-4">
                          Page {paidUsersPage} of {Math.ceil(paidUsersTotal / 10)}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => loadPaidUsers(paidUsersPage + 1)}
                          disabled={paidUsersPage >= Math.ceil(paidUsersTotal / 10)}
                        >
                          Next
                        </Button>
                      </div>
                    )}
                  </div>
                )}
              </Card>
            </TabsContent>
          )}
        </Tabs>
      </div>
    );
  }

  // Ensure all Neo4j integers are converted to regular numbers/strings
  const safeEvent = event ? convertNeo4jIntegers(event) : null;
  const eventIdString = safeEvent?.id ? String(safeEvent.id) : "";

  const [activeCommentTab, setActiveCommentTab] = useState<"comments" | "live">("comments");
  const isLive = safeEvent?.status === "live";

  // Convert all Neo4j integers in recommendations
  const safeRecommendations = recommendations.map(rec => {
    const safeRec = convertNeo4jIntegers(rec);
    return {
      ...safeRec,
      id: String(safeRec.id) // Ensure ID is a string
    };
  });

  return (
    <div className="container max-w-[1800px] py-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <div className="relative aspect-video bg-black rounded-lg overflow-hidden mb-4">
            <img
              src={safeEvent?.imageUrl || '/placeholder.svg'}
              alt={safeEvent?.title}
              className="w-full h-full object-cover"
            />
            {safeEvent?.status && (
              <div className={`absolute top-2 right-2 px-2 py-1 text-sm rounded-md ${
                safeEvent.status === 'live' ? 'bg-red-600 text-white' :
                safeEvent.status === 'draft' ? 'bg-gray-600 text-white' :
                safeEvent.status === 'published' ? 'bg-blue-600 text-white' :
                safeEvent.status === 'upcoming' ? 'bg-yellow-600 text-white' :
                'bg-gray-600 text-white'
              }`}>
                {safeEvent.status === 'live' ? 'LIVE' :
                 safeEvent.status === 'draft' ? 'DRAFT' :
                 safeEvent.status === 'published' ? 'PUBLISHED' :
                 safeEvent.status === 'upcoming' ? 'UPCOMING' : 'ENDED'}
              </div>
            )}
          </div>

          <h1 className="text-xl font-bold mb-2">{safeEvent?.title}</h1>

          <div className="flex flex-wrap items-center justify-between gap-4 mb-4">
            <div className="flex items-center gap-4">
              <div
                className="flex items-center gap-3 cursor-pointer hover:text-primary"
                onClick={() => {
                  if (event?.channel?.id) {
                    router.push(`/channel-view/${event.channel.id}`);
                  }
                }}
              >
                <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center overflow-hidden">
                  {channel?.imageUrl ? (
                    <img
                      src={channel.imageUrl}
                      alt={channelName}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <UserRound className="h-5 w-5" />
                  )}
                </div>
                <div>
                  <h3 className="font-medium hover:text-primary">{channelName}</h3>
                  <p className="text-sm text-muted-foreground">{subscribersCount} subscribers</p>
                </div>
              </div>
              {isAuthenticated() && (
                <Button
                  variant="default"
                  className="rounded-full"
                  onClick={() => {
                    if (event?.channel?.id) {
                      router.push(`/channel-view/${event.channel.id}`);
                    }
                  }}
                >
                  View Channel
                </Button>
              )}
            </div>

            <div className="flex items-center gap-2">
              <div className="flex">
                <Button
                  variant="outline"
                  className="rounded-l-full rounded-r-none flex gap-1"
                  onClick={handleLike}
                >
                  <ThumbsUp className={`h-4 w-4 ${isLiked ? "text-blue-500 fill-blue-500" : ""}`} />
                  <span>{likeCount}</span>
                </Button>
                <Button variant="outline" className="rounded-l-none rounded-r-full border-l-0">
                  <ThumbsDown className="h-4 w-4" />
                </Button>
              </div>
              <Button variant="outline" className="rounded-full">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
              {/* Show Watch Now button for published/live events if user has access */}
              {(safeEvent?.status === 'published' || safeEvent?.status === 'live') && hasAccess ? (
                <Button
                  variant="default"
                  className="rounded-full bg-[#8B5CF6] hover:bg-[#7C3AED]"
                  onClick={() => router.push(`/event/watch/${eventIdString}`)}
                >
                  <Play className="h-4 w-4 mr-2" />
                  Watch Now
                </Button>
              ) : (
                <Button
                  variant="default"
                  className="rounded-full bg-[#8B5CF6] hover:bg-[#7C3AED]"
                  onClick={handlePurchaseEvent}
                >
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  Purchase ({safeEvent?.price} {safeEvent?.currency})
                </Button>
              )}
            </div>
          </div>

          <Card className="p-4 mb-4">
            <div className="flex gap-4 text-sm text-muted-foreground mb-2">
              <span>{views} views</span>
              <span>{formatRelativeDate(safeEvent?.startTime)}</span>
            </div>
            <p className="text-sm whitespace-pre-wrap">{safeEvent?.description}</p>

            {/* Event Schedule Information */}
            <div className="mt-4 pt-4 border-t space-y-3">
              <h4 className="text-sm font-medium flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Event Schedule
              </h4>

              {/* Start Time */}
              {safeEvent?.startTime && (
                <div className="space-y-1">
                  <div className="flex items-center gap-2 text-sm font-medium">
                    <Clock className="h-3 w-3" />
                    <span>Start Time:</span>
                  </div>
                  {(() => {
                    const startTimeInfo = formatDateWithTimezone(safeEvent.startTime, safeEvent.timezone);
                    const browserTimezone = getBrowserTimezone();
                    const showBrowserTime = safeEvent.timezone && safeEvent.timezone !== browserTimezone;

                    return (
                      <div className="space-y-1">
                        <div className="text-sm">
                          {startTimeInfo.eventTime}
                          {safeEvent.timezone && (
                            <span className="text-muted-foreground ml-1">
                              ({formatTimezone(safeEvent.timezone)})
                            </span>
                          )}
                        </div>
                        {showBrowserTime && (
                          <div className="text-xs text-muted-foreground">
                            Your time: {startTimeInfo.browserTime}
                          </div>
                        )}
                      </div>
                    );
                  })()}
                </div>
              )}

              {/* End Time */}
              {safeEvent?.endTime && (
                <div className="space-y-1">
                  <div className="flex items-center gap-2 text-sm font-medium">
                    <Clock className="h-3 w-3" />
                    <span>End Time:</span>
                  </div>
                  {(() => {
                    const endTimeInfo = formatDateWithTimezone(safeEvent.endTime, safeEvent.timezone);
                    const browserTimezone = getBrowserTimezone();
                    const showBrowserTime = safeEvent.timezone && safeEvent.timezone !== browserTimezone;

                    return (
                      <div className="space-y-1">
                        <div className="text-sm">
                          {endTimeInfo.eventTime}
                          {safeEvent.timezone && (
                            <span className="text-muted-foreground ml-1">
                              ({formatTimezone(safeEvent.timezone)})
                            </span>
                          )}
                        </div>
                        {showBrowserTime && (
                          <div className="text-xs text-muted-foreground">
                            Your time: {endTimeInfo.browserTime}
                          </div>
                        )}
                      </div>
                    );
                  })()}
                </div>
              )}
            </div>

            <div className="mt-4 pt-4 border-t grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
              {safeEvent?.category && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Category:</span>
                  <span>{categories.find(c => String(c.id) === String(safeEvent.category))?.name || safeEvent.category}</span>
                </div>
              )}
              {safeEvent?.pricingPlanId && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Pricing Plan:</span>
                  <span>
                    {pricingPlans.find(p => String(p.id) === String(safeEvent.pricingPlanId))?.product || safeEvent.pricingPlanId}
                  </span>
                </div>
              )}
              {safeEvent?.forKids !== undefined && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">For Kids:</span>
                  <span>{safeEvent.forKids ? 'Yes' : 'No'}</span>
                </div>
              )}
              {safeEvent?.viewersOverEighteen !== undefined && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">18+ Content:</span>
                  <span>{safeEvent.viewersOverEighteen ? 'Yes' : 'No'}</span>
                </div>
              )}
            </div>
          </Card>

          <Card className="mb-4">
            <div className="p-4 border-b">
              <div className="flex items-center gap-4 mb-4">
                <Button
                  variant={activeCommentTab === "comments" ? "default" : "outline"}
                  onClick={() => setActiveCommentTab("comments")}
                >
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Comments
                </Button>
                {isLive && (
                  <Button
                    variant={activeCommentTab === "live" ? "default" : "outline"}
                    onClick={() => setActiveCommentTab("live")}
                  >
                    <MessageCircle className="h-4 w-4 mr-2" fill="currentColor" />
                    Live Chat
                  </Button>
                )}
              </div>

              {activeCommentTab === "comments" ? (
                <CommentSection eventId={eventIdString} />
              ) : (
                <>
                  <div className="flex gap-2">
                    <Textarea
                      placeholder={isAuthenticated() ? "Send a message..." : "Sign in to chat"}
                      disabled={!isAuthenticated()}
                      className="min-h-[60px]"
                    />
                    <Button
                      className="flex-shrink-0 h-full"
                      disabled={!isAuthenticated()}
                      variant="default"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>

                  <ScrollArea className="h-[400px]">
                    <div className="p-4 text-center text-muted-foreground">
                      {isAuthenticated() ? "No messages yet" : "Sign in to participate in live chat"}
                    </div>
                  </ScrollArea>
                </>
              )}
            </div>
          </Card>

          {/* Admin-only paid users section */}
          {isAdmin && (
            <Card className="mb-4">
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Paid Users (Admin View)
                </h3>
                {paidUsersLoading && paidUsers.length === 0 ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => loadPaidUsers(1)}
                      disabled={paidUsersLoading}
                    >
                      {paidUsersLoading ? "Loading..." : "Load Paid Users"}
                    </Button>

                    {paidUsers.length > 0 && (
                      <>
                        <div className="overflow-x-auto">
                          <table className="w-full text-sm">
                            <thead>
                              <tr className="border-b">
                                <th className="text-left py-2 px-2">User</th>
                                <th className="text-left py-2 px-2">Email</th>
                                <th className="text-left py-2 px-2">Amount</th>
                                <th className="text-left py-2 px-2">Date</th>
                              </tr>
                            </thead>
                            <tbody>
                              {paidUsers.map((purchase, index) => (
                                <tr key={index} className="border-b">
                                  <td className="py-2 px-2">
                                    {purchase.firstName} {purchase.lastName}
                                    {purchase.username && <span className="text-xs text-muted-foreground block">@{purchase.username}</span>}
                                  </td>
                                  <td className="py-2 px-2 text-xs">{purchase.email}</td>
                                  <td className="py-2 px-2">${purchase.amount?.toFixed(2) || '0.00'}</td>
                                  <td className="py-2 px-2 text-xs">
                                    {purchase.purchaseDate ? formatDate(purchase.purchaseDate) : 'N/A'}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>

                        {/* Pagination */}
                        {paidUsersTotal > 10 && (
                          <div className="flex justify-center gap-2 mt-4">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => loadPaidUsers(paidUsersPage - 1)}
                              disabled={paidUsersPage === 1 || paidUsersLoading}
                            >
                              Previous
                            </Button>
                            <span className="py-2 px-4 text-sm">
                              Page {paidUsersPage} of {Math.ceil(paidUsersTotal / 10)}
                            </span>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => loadPaidUsers(paidUsersPage + 1)}
                              disabled={paidUsersPage >= Math.ceil(paidUsersTotal / 10) || paidUsersLoading}
                            >
                              Next
                            </Button>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                )}
              </div>
            </Card>
          )}
        </div>

        <div className="space-y-4">
          <h2 className="font-medium">More from this channel</h2>
          <div className="space-y-4">
            {safeRecommendations.map(rec => (
              <Card
                key={rec.id}
                className="overflow-hidden cursor-pointer"
                onClick={() => router.push(`/event/${rec.id}`)}
              >
                <div className="aspect-video">
                  <img
                    src={rec.imageUrl || '/placeholder.svg'}
                    alt={rec.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="p-2">
                  <h3 className="font-medium text-sm line-clamp-2">{rec.title}</h3>
                  <p className="text-xs text-muted-foreground mt-1">
                    {rec.startTime ? formatRelativeDate(rec.startTime) : ''}
                  </p>
                </div>
              </Card>
            ))}
            {safeRecommendations.length === 0 && (
              <p className="text-muted-foreground text-sm">No other events available from this channel</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}