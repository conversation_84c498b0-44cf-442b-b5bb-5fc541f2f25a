"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAppContext } from "@/context/NextAppContext";
import { Button } from "@/components/ui/button";
import { PlusIcon, UserRound, Edit } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { fetchUserChannels } from "@/services/channel-service";
import { Channel } from "@/interfaces/types";
import { isAuthenticated } from "@/lib/auth";

export default function ChannelsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [channels, setChannels] = useState<Channel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadChannels = async () => {
      try {
        setLoading(true);

        if (!isAuthenticated()) {
          router.push("/login");
          return;
        }

        const data = await fetchUserChannels();
        setChannels(data);
      } catch (err) {
        console.error('Failed to load channels', err);
        setError('Failed to load channels');
        toast({
          title: "Error",
          description: "Failed to load channels",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    loadChannels();
  }, [router, toast]);

  if (loading) {
    return (
      <div className="container py-8">
        <div className="flex justify-center items-center h-60">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container py-8">
        <div className="text-center py-12 border rounded-lg">
          <p className="text-red-500 mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>Try Again</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4">
        <div>
          <h1 className="text-2xl font-bold">My Channels</h1>
          <p className="text-muted-foreground">Manage your channels and events</p>
        </div>

        <Button
          onClick={() => router.push('/channel-create')}
          className="flex items-center gap-2"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Create Channel</span>
        </Button>
      </div>

      {channels.length === 0 ? (
        <div className="text-center py-12 border rounded-lg">
          <UserRound className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No channels yet</h3>
          <p className="text-muted-foreground mb-4">Create your first channel to get started</p>
          <Button onClick={() => router.push('/channel-create')}>
            Create Channel
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {channels.map(channel => (
            <Card key={channel.id} className="overflow-hidden">
              <div className="h-32 bg-muted relative">
                {channel.bannerUrl && (
                  <img
                    src={channel.bannerUrl}
                    alt={`${channel.name} banner`}
                    className="w-full h-full object-cover"
                  />
                )}
              </div>
              <CardHeader className="flex flex-row items-center gap-4">
                <div className="h-16 w-16 rounded-md bg-muted flex items-center justify-center overflow-hidden">
                  {channel.imageUrl ? (
                    <img
                      src={channel.imageUrl}
                      alt={channel.name}
                      className="h-full w-full object-cover"
                    />
                  ) : (
                    <UserRound className="h-8 w-8" />
                  )}
                </div>
                <div>
                  <CardTitle>{channel.name}</CardTitle>
                  <CardDescription className="line-clamp-1">
                    {channel.description}
                  </CardDescription>
                  <div className="flex items-center mt-1">
                    <span className={`inline-flex items-center px-2 py-1 text-xs rounded-full ${
                      channel.status === 'active'
                        ? 'bg-green-100 text-green-800'
                        : channel.status === 'pending'
                        ? 'bg-yellow-100 text-yellow-800'
                        : channel.status === 'pending_approval'
                        ? 'bg-amber-100 text-amber-800'
                        : channel.status === 'draft'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {channel.status === 'active' ? 'Active' :
                       channel.status === 'pending' ? 'Pending' :
                       channel.status === 'pending_approval' ? 'Pending Approval' :
                       channel.status === 'draft' ? 'Draft' : 'Rejected'}
                    </span>
                  </div>
                </div>
              </CardHeader>
              <CardFooter className="flex justify-between gap-2">
                <Button
                  variant="outline"
                  className="flex-1 flex items-center gap-2"
                  onClick={() => router.push(`/channel/${channel.id}`)}
                >
                  <Edit className="h-4 w-4" />
                  View
                </Button>
                <Button
                  variant="outline"
                  className="flex-1 flex items-center gap-2"
                  onClick={() => router.push(`/channel-edit/${channel.id}`)}
                >
                  <Edit className="h-4 w-4" />
                  Edit Channel
                </Button>

              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}