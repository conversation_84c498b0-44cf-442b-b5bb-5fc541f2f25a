
"use client";

import { useState } from "react";
import { Heading } from "@/components/ui/heading";
import { useAppContext } from "@/context/NextAppContext";
import { Button } from "@/components/ui/button";
import { CreditCard } from "lucide-react"; // Replacing PayPal with CreditCard icon
import { toast } from "sonner";

export default function PaymentSettingsPage() {
  const { user } = useAppContext();
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);

  const handleConnect = () => {
    setIsConnecting(true);
    // Simulate PayPal connection
    setTimeout(() => {
      setIsConnected(true);
      setIsConnecting(false);
      toast.success("Successfully connected to PayPal");
    }, 1500);
  };

  const handleDisconnect = () => {
    setIsConnecting(true);
    // Simulate PayPal disconnection
    setTimeout(() => {
      setIsConnected(false);
      setIsConnecting(false);
      toast.success("Successfully disconnected from PayPal");
    }, 1500);
  };

  return (
    <div className="container py-8">
      <Heading
        title="Payout & Tax Settings"
        description="Manage your payment methods and tax information"
        className="mb-8"
      />

      <div className="max-w-3xl mx-auto space-y-8">
        <div>
          <h2 className="text-xl font-semibold mb-4">Payout Method</h2>
          <div className="bg-card rounded-lg border p-6">
            <div className="text-sm text-muted-foreground mb-6">
              <p>Choose your payout method below.</p>
              <p className="mt-2">
                Connecting to a new payout method may take a few days. You won't
                receive payments to the new linked account until its status is
                approved.
              </p>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-4">
                <CreditCard className="h-6 w-6" /> {/* Using CreditCard instead of PayPal */}
                <div>
                  <h3 className="font-medium">PayPal</h3>
                  {isConnected && (
                    <p className="text-sm text-muted-foreground">
                      Connected as {user?.email}
                    </p>
                  )}
                </div>
              </div>

              <Button
                onClick={isConnected ? handleDisconnect : handleConnect}
                disabled={isConnecting}
                variant={isConnected ? "destructive" : "default"}
              >
                {isConnecting
                  ? "Processing..."
                  : isConnected
                  ? "Disconnect"
                  : "Connect"}
              </Button>
            </div>
          </div>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">Tax Information</h2>
          <div className="bg-card rounded-lg border p-6">
            <p className="text-sm text-muted-foreground mb-4">
              Your tax documentation will be used to determine the appropriate
              withholding tax rate for your earnings.
            </p>
            <Button variant="outline">Submit Tax Information</Button>
          </div>
        </div>
      </div>
    </div>
  );
}
