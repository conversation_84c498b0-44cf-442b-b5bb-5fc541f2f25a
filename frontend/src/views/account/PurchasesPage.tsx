
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAppContext } from "@/context/NextAppContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { formatDistanceToNow } from "date-fns";
import { Ticket, PlayCircle, Loader2 } from "lucide-react";
import { fetchPurchasedEvents } from "@/services/event-service";
import { EventPurchase } from "@/interfaces/types";
import { useToast } from "@/hooks/use-toast";
import { neo4jDateTimeToDate, isNeo4jDateTime, isValidDate } from "@/utils/neo4j-utils";

export default function PurchasesPage() {
  const router = useRouter();
  const { user } = useAppContext();
  const { toast } = useToast();
  const [purchases, setPurchases] = useState<EventPurchase[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Helper function to format purchase date
  const formatPurchaseDate = (dateValue: any) => {
    try {
      // Default fallback
      let formattedDate = 'Recently';

      // Handle Neo4j datetime objects
      if (isNeo4jDateTime(dateValue)) {
        try {
          // Extract date components directly
          const { year, month, day } = dateValue;

          // Only proceed if we have valid year, month, and day
          if (typeof year === 'number' && typeof month === 'number' && typeof day === 'number') {
            // Create a simple date string in ISO format
            const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
            const date = new Date(dateStr);

            if (!isNaN(date.getTime())) {
              formattedDate = formatDistanceToNow(date, { addSuffix: true });
            }
          }
        } catch (e) {
          console.warn('Failed to format Neo4j datetime:', e, dateValue);
        }
      }
      // Handle string dates
      else if (typeof dateValue === 'string') {
        try {
          const date = new Date(dateValue);
          if (!isNaN(date.getTime())) {
            formattedDate = formatDistanceToNow(date, { addSuffix: true });
          }
        } catch (e) {
          console.warn('Invalid date string:', dateValue, e);
        }
      }
      // Handle Date objects
      else if (dateValue instanceof Date) {
        if (!isNaN(dateValue.getTime())) {
          formattedDate = formatDistanceToNow(dateValue, { addSuffix: true });
        }
      }

      return formattedDate;
    } catch (err) {
      console.error('Error formatting date:', err, dateValue);
      return 'Recently';
    }
  };

  useEffect(() => {
    const loadPurchases = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch purchases data
        let data;
        try {
          data = await fetchPurchasedEvents();
          console.log('Fetched purchases:', data);
        } catch (fetchError) {
          console.error('Error in fetchPurchasedEvents:', fetchError);
          throw new Error('Failed to fetch purchases data from the server');
        }

        // Validate the data structure
        if (!data) {
          throw new Error('No data received from the server');
        }

        if (!Array.isArray(data)) {
          console.error('Invalid purchases data format:', data);
          throw new Error('Invalid response format: expected an array of purchases');
        }

        // Filter out any invalid purchases (missing required fields)
        const validPurchases = data.filter(purchase => {
          try {
            if (!purchase) {
              console.warn('Skipping null or undefined purchase');
              return false;
            }

            if (typeof purchase !== 'object') {
              console.warn('Skipping non-object purchase:', purchase);
              return false;
            }

            if (!purchase.id) {
              console.warn('Skipping purchase without ID:', purchase);
              return false;
            }

            if (!purchase.event || typeof purchase.event !== 'object') {
              console.warn('Skipping purchase without valid event data:', purchase);
              return false;
            }

            return true;
          } catch (filterError) {
            console.error('Error filtering purchase:', filterError, purchase);
            return false;
          }
        });

        console.log(`Filtered ${data.length} purchases to ${validPurchases.length} valid purchases`);
        setPurchases(validPurchases);
      } catch (err) {
        console.error('Error fetching purchases:', err);
        setError('Failed to load your purchases. Please try again later.');
        toast({
          title: "Error",
          description: "Failed to load your purchases. Please try again later.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      loadPurchases();
    }
  }, [user, toast]);

  if (!user) {
    router.push("/login");
    return null;
  }

  return (
    <div className="container py-8">
      <div className="mb-8">
        <h1 className="text-2xl font-bold">My Purchases</h1>
        <p className="text-muted-foreground">Access your purchased events and content</p>
      </div>

      {loading ? (
        <div className="flex flex-col items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
          <p className="text-muted-foreground">Loading your purchases...</p>
        </div>
      ) : error ? (
        <div className="text-center py-12 border rounded-lg">
          <h3 className="text-lg font-medium mb-2 text-destructive">Error Loading Purchases</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={() => router.push('/home')}>
            Go to Home
          </Button>
        </div>
      ) : purchases.length === 0 ? (
        <div className="text-center py-12 border rounded-lg">
          <Ticket className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No purchases yet</h3>
          <p className="text-muted-foreground mb-4">Explore events to watch premium content</p>
          <Button onClick={() => router.push('/home')}>
            Explore Events
          </Button>
        </div>
      ) : (
        <div className="space-y-4">
          {purchases.map(purchase => {
            if (!purchase || !purchase.id) {
              console.warn('Invalid purchase data:', purchase);
              return null;
            }

            try {
              // Safely extract event data with fallbacks
              const event = purchase.event;
              const eventId = event?.id || '';
              const eventTitle = event?.title || 'Unknown Event';
              const channelName = event?.channel?.name || 'Unknown Channel';
              const purchaseStatus = purchase.status || 'completed';
              const currency = purchase.currency?.toUpperCase() || 'USD';
              const amount = typeof purchase.amount === 'number' ? purchase.amount.toFixed(2) : '0.00';

              // Format the purchase date safely
              let purchaseDate = 'Recently';
              try {
                purchaseDate = formatPurchaseDate(purchase.purchasedAt);
              } catch (dateError) {
                console.error('Error formatting purchase date:', dateError);
              }

              return (
                <Card key={purchase.id}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle>{eventTitle}</CardTitle>
                        <CardDescription>
                          Purchased {purchaseDate}
                        </CardDescription>
                        <CardDescription className="mt-1">
                          By {channelName}
                        </CardDescription>
                      </div>
                      <div className="flex flex-col items-end">
                        <span className="font-semibold">
                          {currency} {amount}
                        </span>
                        <span className={`text-xs ${purchaseStatus === 'refunded' ? 'text-destructive' : 'text-muted-foreground'}`}>
                          {purchaseStatus.charAt(0).toUpperCase() + purchaseStatus.slice(1)}
                        </span>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="flex justify-end">
                    {purchaseStatus === 'completed' && eventId && (
                      <Button
                        onClick={() => router.push(`/event/watch/${eventId}`)}
                        className="flex items-center gap-2"
                      >
                        <PlayCircle className="h-4 w-4" />
                        Watch Event
                      </Button>
                    )}
                    {purchaseStatus === 'refunded' && eventId && (
                      <Button
                        variant="outline"
                        onClick={() => router.push(`/event/${eventId}`)}
                      >
                        View Details
                      </Button>
                    )}
                  </CardContent>
                </Card>
              );
            } catch (error) {
              console.error('Error rendering purchase card:', error, purchase);
              return null;
            }
          }).filter(Boolean)}
        </div>
      )}
    </div>
  );
}
