
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Shield, PlusCircle, Trash2, Loader2 } from "lucide-react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { startWebAuthnRegistration, finishWebAuthnRegistration, getUserCredentials, deleteUserCredential, generateTotp, completeTotp } from "@/services/auth-service";
import { WebAuthnService } from "@/services/webauthn-service";
import { useAppContext } from "@/context/NextAppContext";
import { MFAMethod } from "@/lib/types";

export default function MFAManagementPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { user } = useAppContext();
  const [mfaMethods, setMfaMethods] = useState<MFAMethod[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState<MFAMethod | null>(null);
  const [newMethodType, setNewMethodType] = useState<"authenticator" | "passkey">("passkey");
  const [verificationCode, setVerificationCode] = useState("");
  const [isRegistering, setIsRegistering] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // TOTP-specific state
  const [totpSecret, setTotpSecret] = useState("");
  const [totpQRCode, setTotpQRCode] = useState("");
  const [totpGenerated, setTotpGenerated] = useState(false);

  // Load user credentials on component mount
  useEffect(() => {
    loadUserCredentials();
  }, []);

  const loadUserCredentials = async () => {
    try {
      setIsLoading(true);
      const response = await getUserCredentials();

      if (response.error) {
        toast({
          title: "Error",
          description: response.error,
          variant: "destructive",
        });
      } else {
        // Map the credentials to match the expected format
        const mappedCredentials: MFAMethod[] = response.credentials.map((credential) => ({
          id: credential.id,
          type: mapCredentialType(credential.type),
          userLabel: credential.userLabel,
          createdDate: credential.createdDate,
        }));
        setMfaMethods(mappedCredentials);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load MFA methods. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const mapCredentialType = (type: string): "authenticator" | "passkey" => {
    switch (type) {
      case 'webauthn':
        return 'passkey';
      case 'custom-webauthn':
        return 'passkey';
      case 'otp':
        return 'authenticator';
      default:
        return 'authenticator';
    }
  };

  const handleAddMFA = async () => {
    if (newMethodType === "passkey") {
      await handleWebAuthnRegistration();
    } else if (newMethodType === "authenticator") {
      if (!totpGenerated) {
        await handleGenerateTotp();
      } else {
        await handleCompleteTotp();
      }
    }
  };

  const handleWebAuthnRegistration = async () => {
    if (!user) {
      toast({
        title: "Error",
        description: "User information not available. Please refresh and try again.",
        variant: "destructive",
      });
      return;
    }

    setIsRegistering(true);

    try {
      // Check WebAuthn support
      if (!WebAuthnService.isSupported()) {
        toast({
          title: "Not Supported",
          description: "WebAuthn is not supported in this browser.",
          variant: "destructive",
        });
        return;
      }

      if (!WebAuthnService.isSecureContext()) {
        toast({
          title: "Secure Connection Required",
          description: "WebAuthn requires a secure connection (HTTPS).",
          variant: "destructive",
        });
        return;
      }

      // Start WebAuthn registration
      const startResponse = await startWebAuthnRegistration({
        fullName: `${user.firstName} ${user.lastName}`,
        email: user.email
      });

      if (!startResponse) {
        toast({
          title: "Registration Failed",
          description: "Failed to start WebAuthn registration. Please try again.",
          variant: "destructive",
        });
        return;
      }

      // Prepare credential creation options
      const creationOptions = WebAuthnService.prepareCredentialCreationOptions(
        startResponse.credentialCreationOptions
      );

      // Create credential
      const credential = await WebAuthnService.createCredential(creationOptions);
      if (!credential) {
        toast({
          title: "Registration Cancelled",
          description: "WebAuthn registration was cancelled or failed.",
          variant: "destructive",
        });
        return;
      }

      // Serialize credential for server
      const serializedCredential = WebAuthnService.serializeCredentialForRegistration(credential);

      // Finish registration
      const finishResponse = await finishWebAuthnRegistration({
        reference: startResponse.reference,
        credential: serializedCredential
      });

      if (finishResponse.success) {
        // Refresh credentials from server
        await loadUserCredentials();
        setIsAddDialogOpen(false);

        toast({
          title: "WebAuthn Registered",
          description: "Your security key/passkey has been successfully registered.",
        });
      } else {
        toast({
          title: "Registration Failed",
          description: finishResponse.message || "Failed to complete WebAuthn registration.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error('WebAuthn registration error:', error);
      toast({
        title: "Registration Failed",
        description: WebAuthnService.getErrorMessage(error),
        variant: "destructive",
      });
    } finally {
      setIsRegistering(false);
    }
  };

  const handleGenerateTotp = async () => {
    setIsRegistering(true);

    try {
      const response = await generateTotp();

      if (response.success) {
        setTotpSecret(response.encodedTotpSecret);
        setTotpQRCode(response.totpSecretQRCode);
        setTotpGenerated(true);

        toast({
          title: "QR Code Generated",
          description: "Scan the QR code with your authenticator app and enter the verification code.",
        });
      } else {
        toast({
          title: "Generation Failed",
          description: response.message || "Failed to generate TOTP QR code. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Generation Failed",
        description: "An error occurred while generating the TOTP QR code.",
        variant: "destructive",
      });
    } finally {
      setIsRegistering(false);
    }
  };

  const handleCompleteTotp = async () => {
    if (!verificationCode.trim()) {
      toast({
        title: "Verification Required",
        description: "Please enter the verification code from your authenticator app.",
        variant: "destructive",
      });
      return;
    }

    setIsRegistering(true);

    try {
      const response = await completeTotp({
        deviceName: "Authenticator App",
        encodedTotpSecret: totpSecret,
        totpInitialCode: verificationCode,
        overwrite: false
      });

      if (response.success) {
        // Refresh credentials from server
        await loadUserCredentials();
        setIsAddDialogOpen(false);
        resetTotpState();

        toast({
          title: "Authenticator App Added",
          description: "Your authenticator app has been successfully configured.",
        });
      } else {
        toast({
          title: "Setup Failed",
          description: response.message || "Failed to complete authenticator app setup. Please check your verification code.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Setup Failed",
        description: "An error occurred while setting up the authenticator app.",
        variant: "destructive",
      });
    } finally {
      setIsRegistering(false);
    }
  };

  const resetTotpState = () => {
    setTotpSecret("");
    setTotpQRCode("");
    setTotpGenerated(false);
    setVerificationCode("");
  };

  const handleDeleteMFA = async () => {
    if (!selectedMethod) return;

    try {
      setIsDeleting(true);
      const response = await deleteUserCredential(selectedMethod.id);

      if (response.success) {
        // Refresh credentials from server
        await loadUserCredentials();
        setIsDeleteDialogOpen(false);
        setSelectedMethod(null);

        toast({
          title: "MFA Removed",
          description: "The authentication method has been removed from your account",
        });
      } else {
        toast({
          title: "Delete Failed",
          description: response.message || "Failed to delete the authentication method",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Delete Failed",
        description: "An error occurred while deleting the authentication method",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const getMethodName = (type: string) => {
    switch(type) {
      case "authenticator": return "Authenticator App";
      case "passkey": return "Passkey";
      default: return type;
    }
  };

  const getMethodIcon = (type: string) => {
    return <Shield className="h-5 w-5" />;
  };

  return (
    <div className="container max-w-2xl py-10">
      <div className="flex items-center justify-center mb-8">
        <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
          <Shield className="h-6 w-6 text-primary" />
        </div>
      </div>

      <h2 className="text-2xl font-bold text-center mb-1">Multi-Factor Authentication</h2>
      <p className="text-muted-foreground text-center mb-6">
        Secure your account with multiple authentication methods
      </p>

      <div className="space-y-4">
        {isLoading ? (
          <div className="text-center py-12 border rounded-lg">
            <Loader2 className="h-12 w-12 text-muted-foreground mx-auto mb-4 animate-spin" />
            <h3 className="text-lg font-medium mb-2">Loading MFA methods...</h3>
            <p className="text-muted-foreground">Please wait while we fetch your authentication methods</p>
          </div>
        ) : mfaMethods.length === 0 ? (
          <div className="text-center py-12 border rounded-lg">
            <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No MFA methods enabled</h3>
            <p className="text-muted-foreground mb-4">Add a multi-factor authentication method to secure your account</p>
          </div>
        ) : (
          mfaMethods.map((method) => (
            <Card key={method.id}>
              <CardHeader>
                <div className="flex justify-between">
                  <div className="flex items-center gap-3">
                    {getMethodIcon(method.type)}
                    <div>
                      <CardTitle>{getMethodName(method.type)}</CardTitle>
                      {method.userLabel && method.userLabel !== getMethodName(method.type) && (
                        <p className="text-sm text-muted-foreground">{method.userLabel}</p>
                      )}
                    </div>
                  </div>
                  <Button
                    variant="destructive"
                    size="icon"
                    onClick={() => {
                      setSelectedMethod(method);
                      setIsDeleteDialogOpen(true);
                    }}
                    disabled={isDeleting}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  {method.createdDate ? (
                    `Added on ${new Date(method.createdDate).toLocaleDateString()}`
                  ) : (
                    'Date not available'
                  )}
                </CardDescription>
              </CardContent>
            </Card>
          ))
        )}

        <div className="flex justify-end">
          <Button
            onClick={() => setIsAddDialogOpen(true)}
            className="flex items-center gap-2"
          >
            <PlusCircle className="h-4 w-4" />
            Add MFA Method
          </Button>
        </div>

        <div className="mt-8">
          <Button onClick={() => router.push("/profile")} variant="outline">
            Back to Profile
          </Button>
        </div>
      </div>

      {/* Add MFA Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={(open) => {
        setIsAddDialogOpen(open);
        if (!open) {
          resetTotpState();
        }
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Authentication Method</DialogTitle>
            <DialogDescription>
              Choose a multi-factor authentication method to add to your account
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <RadioGroup value={newMethodType} onValueChange={(value: any) => setNewMethodType(value)}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="authenticator" id="authenticator" />
                <Label htmlFor="authenticator">Authenticator App</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="passkey" id="passkey" />
                <Label htmlFor="passkey">Passkey</Label>
              </div>
            </RadioGroup>

            {newMethodType === "passkey" && (
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  Click "Add Method" to register your security key or use biometric authentication.
                  Make sure your device supports WebAuthn and you're using a secure connection.
                </p>
              </div>
            )}

            {newMethodType === "authenticator" && !totpGenerated && (
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  Click "Generate QR Code" to create a QR code for your authenticator app.
                </p>
              </div>
            )}

            {newMethodType === "authenticator" && totpGenerated && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.):
                  </p>
                  <div className="flex justify-center p-4 border rounded-lg bg-white">
                    <img
                      src={`data:image/png;base64,${totpQRCode}`}
                      alt="TOTP QR Code"
                      className="max-w-full h-auto"
                    />
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="code">Verification Code</Label>
                  <Input
                    id="code"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                    placeholder="Enter 6-digit code from your app"
                    maxLength={6}
                  />
                  <p className="text-xs text-muted-foreground">
                    Enter the 6-digit code shown in your authenticator app
                  </p>
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)} disabled={isRegistering}>
              Cancel
            </Button>
            <Button onClick={handleAddMFA} disabled={isRegistering}>
              {isRegistering ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {newMethodType === "passkey" ? "Registering..." :
                   newMethodType === "authenticator" && !totpGenerated ? "Generating..." :
                   "Adding..."}
                </>
              ) : (
                newMethodType === "passkey" ? "Add Method" :
                newMethodType === "authenticator" && !totpGenerated ? "Generate QR Code" :
                "Complete Setup"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Remove Authentication Method</DialogTitle>
            <DialogDescription>
              Are you sure you want to remove {selectedMethod && getMethodName(selectedMethod.type)} authentication?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)} disabled={isDeleting}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteMFA} disabled={isDeleting}>
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Removing...
                </>
              ) : (
                "Remove"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
