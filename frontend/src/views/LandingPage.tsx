
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DEMO_EVENTS } from "@/lib/demo-data";
import { ArrowRight, Users, Globe, Shield } from "lucide-react";

export default function LandingPage() {
  const router = useRouter();

  // Use a few events from demo data as featured events
  const featuredEvents = DEMO_EVENTS.slice(0, 3);

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-primary/10 py-20">
        <div className="container mx-auto px-4 flex flex-col lg:flex-row items-center gap-12">
          <div className="flex-1 space-y-6">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
              Stream Your Live Events to the World
            </h1>
            <p className="text-xl text-muted-foreground">
              Create, host, and monetize your livestream events with our powerful platform designed for creators of all sizes.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" onClick={() => router.push("/register")} className="text-lg px-8">
                Get Started
              </Button>
              <Button size="lg" variant="outline" onClick={() => router.push("/login")} className="text-lg">
                Log In
              </Button>
            </div>
          </div>
          <div className="flex-1 relative">
            <div className="aspect-video bg-gradient-to-br from-brand-purple to-brand-teal/50 rounded-lg shadow-xl overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1605810230434-7631ac76ec81?q=80&w=1200"
                alt="Live streaming setup"
                className="w-full h-full object-cover mix-blend-overlay"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Why Choose Event Horizon</h2>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-background rounded-lg p-6 text-center shadow-sm border">
              <div className="mx-auto w-12 h-12 flex items-center justify-center rounded-full bg-primary/10 mb-4">
                <Globe className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Global Reach</h3>
              <p className="text-muted-foreground">Stream to viewers worldwide with our robust infrastructure designed for scale.</p>
            </div>

            <div className="bg-background rounded-lg p-6 text-center shadow-sm border">
              <div className="mx-auto w-12 h-12 flex items-center justify-center rounded-full bg-primary/10 mb-4">
                <Shield className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Secure Platform</h3>
              <p className="text-muted-foreground">Multi-factor authentication and robust security measures protect your content.</p>
            </div>

            <div className="bg-background rounded-lg p-6 text-center shadow-sm border">
              <div className="mx-auto w-12 h-12 flex items-center justify-center rounded-full bg-primary/10 mb-4">
                <Users className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Community Building</h3>
              <p className="text-muted-foreground">Grow your audience with built-in social features and engagement tools.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Events */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center">Featured Events</h2>

          <div className="grid md:grid-cols-3 gap-8">
            {featuredEvents.map(event => (
              <div key={event.id} className="bg-background rounded-lg overflow-hidden shadow-md border">
                <div className="h-48 overflow-hidden">
                  <img
                    src={event.thumbnail as string}
                    alt={event.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="p-6">
                  <h3 className="font-semibold text-lg mb-2">{event.title}</h3>
                  <p className="text-muted-foreground mb-4 line-clamp-2">{event.description}</p>
                  <Button variant="ghost" className="flex items-center gap-1" onClick={() => router.push("/login")}>
                    Watch Now <ArrowRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-12 text-center">
            <Button size="lg" onClick={() => router.push("/register")}>
              Explore All Events
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
