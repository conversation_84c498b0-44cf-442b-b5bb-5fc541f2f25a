"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Heading } from "@/components/ui/heading";
import { ArrowLeft } from "lucide-react";
import { CreateChannelDialog } from "@/components/channels/CreateChannelDialog";
import { isAuthenticated } from "@/lib/auth";
import { useEffect } from "react";

export default function CreateChannelPage() {
  const router = useRouter();

  // Check authentication
  useEffect(() => {
    if (!isAuthenticated()) {
      router.push("/login");
    }
  }, [router]);

  return (
    <div className="container py-8">
      <Button 
        variant="ghost" 
        className="mb-6 flex items-center gap-2"
        onClick={() => router.push('/account/channels')}
      >
        <ArrowLeft className="h-4 w-4" />
        Back to Channels
      </Button>
      
      <Heading 
        title="Create Channel" 
        description="Submit your channel for approval"
        className="mb-6"
      />
      
      <div className="max-w-3xl mx-auto">
        <div className="bg-muted/50 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Start Your Streaming Channel</h2>
          <p className="text-muted-foreground mb-6">
            Create your own streaming channel and connect with your audience.
            All channels require approval before they can host events.
          </p>
          
          <CreateChannelDialog 
            trigger={
              <Button size="lg" className="gap-2">
                Create New Channel
              </Button>
            } 
          />
        </div>
      </div>
    </div>
  );
}