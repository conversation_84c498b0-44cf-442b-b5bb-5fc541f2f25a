"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAppContext } from "@/context/NextAppContext";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { EventCard } from "@/components/events/EventCard";
import { Bell, Calendar, Clock, Info, Users, ChevronLeft, ChevronRight } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { fetchChannel } from "@/services/channel-service";
import { fetchPublicChannelEvents } from "@/services/event-service";
import {
  checkSubscription,
  subscribeToChannel,
  unsubscribeFromChannel
} from "@/services/subscription-service";
import { Channel, Event } from "@/interfaces/types";
import { isAuthenticated } from "@/lib/auth";
import { formatDate } from "@/utils/date-utils";
import { convertNeo4jIntegers } from "@/utils/neo4j-utils";

interface PublicChannelPageProps {
  id: string;
}

export default function PublicChannelPage({ id }: PublicChannelPageProps) {
  const router = useRouter();
  const { user } = useAppContext();

  const [channel, setChannel] = useState<Channel | null>(null);
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [subscriberCount, setSubscriberCount] = useState(0);
  const [activeTab, setActiveTab] = useState<"all" | "live" | "upcoming" | "past">("all");

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalEvents, setTotalEvents] = useState(0);
  const [eventsPerPage] = useState(12);
  const [paginationLoading, setPaginationLoading] = useState(false);

  // Function to load events with pagination
  const loadEvents = async (page: number) => {
    try {
      setPaginationLoading(true);
      const offset = (page - 1) * eventsPerPage;
      const eventsData = await fetchPublicChannelEvents(id, eventsPerPage, offset);

      setEvents(eventsData.events.map(event => convertNeo4jIntegers(event)));
      setTotalEvents(eventsData.total);
      setCurrentPage(page);
    } catch (err) {
      console.error('Failed to load events', err);
      toast({
        title: "Error",
        description: "Failed to load events",
        variant: "destructive"
      });
    } finally {
      setPaginationLoading(false);
    }
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    if (page !== currentPage && page >= 1 && page <= totalPages) {
      loadEvents(page);
    }
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1);
    }
  };

  // Calculate total pages
  const totalPages = Math.ceil(totalEvents / eventsPerPage);

  useEffect(() => {
    const loadChannelData = async () => {
      try {
        setLoading(true);

        // Load channel details
        const channelData = await fetchChannel(id);
        setChannel(channelData);
        setSubscriberCount(channelData.subscribersCount || 0);

        // Check subscription status if user is authenticated
        if (isAuthenticated()) {
          const subscriptionStatus = await checkSubscription(id);
          setIsSubscribed(subscriptionStatus.subscribed);
        }

        // Load channel events (only published, live, ended) with pagination
        await loadEvents(1); // Load first page
      } catch (err) {
        console.error('Failed to load channel data', err);
        setError('Failed to load channel data');
        toast({
          title: "Error",
          description: "Failed to load channel data",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    loadChannelData();
  }, [id]);

  // Reset to first page when tab changes
  useEffect(() => {
    if (events.length > 0) {
      setCurrentPage(1);
      loadEvents(1);
    }
  }, [activeTab]);

  const handleSubscribe = async () => {
    if (!isAuthenticated()) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to subscribe to this channel",
        variant: "default"
      });
      router.push('/login');
      return;
    }

    try {
      // Optimistic update
      setIsSubscribed(!isSubscribed);
      setSubscriberCount(prev => isSubscribed ? prev - 1 : prev + 1);

      const result = isSubscribed
        ? await unsubscribeFromChannel(id)
        : await subscribeToChannel(id);

      // Update state based on actual result
      setIsSubscribed(result.subscribed);
      setSubscriberCount(result.subscriberCount);

      toast({
        title: result.subscribed ? "Subscribed" : "Unsubscribed",
        description: result.subscribed
          ? `You are now subscribed to ${channel?.name}`
          : `You have unsubscribed from ${channel?.name}`,
      });
    } catch (error) {
      console.error('Failed to update subscription:', error);
      // Revert optimistic update
      setIsSubscribed(!isSubscribed);
      setSubscriberCount(prev => isSubscribed ? prev + 1 : prev - 1);
      toast({
        title: "Error",
        description: "Failed to update subscription. Please try again.",
        variant: "destructive"
      });
    }
  };

  const getFilteredEvents = () => {
    if (!events.length) return [];

    switch (activeTab) {
      case "live":
        return events.filter(event => event.status?.toLowerCase() === 'live');
      case "upcoming":
        return events.filter(event => {
          const eventDate = event.startTime ? new Date(event.startTime) : null;
          return eventDate && eventDate > new Date() && event.status?.toLowerCase() === 'published';
        }).sort((a, b) => {
          const dateA = a.startTime ? new Date(a.startTime).getTime() : 0;
          const dateB = b.startTime ? new Date(b.startTime).getTime() : 0;
          return dateA - dateB; // Sort by closest upcoming date
        });
      case "past":
        return events.filter(event => {
          const eventDate = event.startTime ? new Date(event.startTime) : null;
          return eventDate && eventDate <= new Date() && event.status?.toLowerCase() === 'ended';
        }).sort((a, b) => {
          const dateA = a.startTime ? new Date(a.startTime).getTime() : 0;
          const dateB = b.startTime ? new Date(b.startTime).getTime() : 0;
          return dateB - dateA; // Sort by most recent
        });
      default:
        // For "all" tab, prioritize live events, then upcoming, then past
        const liveEvents = events.filter(event => event.status?.toLowerCase() === 'live');
        const upcomingEvents = events.filter(event => {
          const eventDate = event.startTime ? new Date(event.startTime) : null;
          return eventDate && eventDate > new Date() && event.status?.toLowerCase() === 'published';
        }).sort((a, b) => {
          const dateA = a.startTime ? new Date(a.startTime).getTime() : 0;
          const dateB = b.startTime ? new Date(b.startTime).getTime() : 0;
          return dateA - dateB;
        });
        const pastEvents = events.filter(event => {
          const eventDate = event.startTime ? new Date(event.startTime) : null;
          return eventDate && eventDate <= new Date() && event.status?.toLowerCase() === 'ended';
        }).sort((a, b) => {
          const dateA = a.startTime ? new Date(a.startTime).getTime() : 0;
          const dateB = b.startTime ? new Date(b.startTime).getTime() : 0;
          return dateB - dateA;
        });

        return [...liveEvents, ...upcomingEvents, ...pastEvents];
    }
  };

  if (loading) {
    return (
      <div className="container py-8">
        <div className="flex justify-center items-center h-60">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error || !channel) {
    return (
      <div className="container py-8">
        <div className="text-center py-12 border rounded-lg">
          <h2 className="text-2xl font-bold mb-4">Channel not found</h2>
          <p className="text-red-500 mb-4">{error}</p>
          <Button onClick={() => router.push('/home')}>Back to Home</Button>
        </div>
      </div>
    );
  }

  const filteredEvents = getFilteredEvents();
  const bannerUrl = channel.bannerUrl || '/placeholder-banner.svg';
  const thumbnailUrl = channel.imageUrl || '/placeholder.svg';

  return (
    <div className="flex flex-col min-h-screen">
      {/* Channel Banner */}
      <div className="relative w-full h-48 md:h-64 bg-muted">
        <img
          src={bannerUrl}
          alt={`${channel.name} banner`}
          className="w-full h-full object-cover"
        />
      </div>

      {/* Channel Info */}
      <div className="container py-6">
        <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
          <div className="relative -mt-16 md:-mt-20">
            <Avatar className="h-24 w-24 md:h-32 md:w-32 border-4 border-background">
              <AvatarImage src={thumbnailUrl} alt={channel.name} />
              <AvatarFallback className="text-2xl">{channel.name.charAt(0)}</AvatarFallback>
            </Avatar>
          </div>

          <div className="flex-1">
            <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-4">
              <h1 className="text-2xl md:text-3xl font-bold">{channel.name}</h1>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Users className="h-4 w-4" />
                <span>{subscriberCount} subscribers</span>
              </div>
            </div>
            <p className="text-muted-foreground mt-1">{channel.handle}</p>
          </div>

          <Button
            variant={isSubscribed ? "default" : "outline"}
            className="flex items-center gap-2"
            onClick={handleSubscribe}
          >
            <Bell className={`h-4 w-4 ${isSubscribed ? "fill-white" : ""}`} />
            {isSubscribed ? "Subscribed" : "Subscribe"}
          </Button>
        </div>

        <div className="mt-6">
          <p className="text-sm md:text-base">{channel.description}</p>
        </div>

        <Separator className="my-6" />

        {/* Channel Events */}
        <div>
          <Tabs defaultValue="all" onValueChange={(value) => setActiveTab(value as any)}>
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold">Events</h2>
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="live">Live</TabsTrigger>
                <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
                <TabsTrigger value="past">Past</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="all" className="mt-0">
              {renderEventsList(filteredEvents)}
              {renderPagination()}
            </TabsContent>
            <TabsContent value="live" className="mt-0">
              {renderEventsList(filteredEvents)}
              {renderPagination()}
            </TabsContent>
            <TabsContent value="upcoming" className="mt-0">
              {renderEventsList(filteredEvents)}
              {renderPagination()}
            </TabsContent>
            <TabsContent value="past" className="mt-0">
              {renderEventsList(filteredEvents)}
              {renderPagination()}
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );

  function renderEventsList(events: Event[]) {
    if (paginationLoading) {
      return (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground mt-4">Loading events...</p>
        </div>
      );
    }

    if (events.length === 0) {
      return (
        <div className="text-center py-12 border rounded-lg">
          <Calendar className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-medium mb-2">No events found</h3>
          <p className="text-muted-foreground">
            {activeTab === "live"
              ? "There are no live events at the moment."
              : activeTab === "upcoming"
                ? "There are no upcoming events scheduled."
                : activeTab === "past"
                  ? "There are no past events to show."
                  : "This channel hasn't published any events yet."}
          </p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {events.map((event) => (
          <EventCard
            key={event.id}
            event={event}
            hideSubscribeButton
            onWatch={() => router.push(`/event/watch/${event.id}`)}
          />
        ))}
      </div>
    );
  }

  function renderPagination() {
    if (totalPages <= 1) return null;

    return (
      <div className="mt-8">
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <Button
                onClick={handlePrevPage}
                disabled={currentPage === 1 || paginationLoading}
                variant="ghost"
                size="sm"
                className="gap-1"
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
            </PaginationItem>

            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              // Show pages around current page
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (currentPage <= 3) {
                pageNum = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = currentPage - 2 + i;
              }

              return (
                <PaginationItem key={pageNum}>
                  <Button
                    onClick={() => handlePageChange(pageNum)}
                    variant={currentPage === pageNum ? "outline" : "ghost"}
                    size="sm"
                    disabled={paginationLoading}
                  >
                    {pageNum}
                  </Button>
                </PaginationItem>
              );
            })}

            <PaginationItem>
              <Button
                onClick={handleNextPage}
                disabled={currentPage === totalPages || paginationLoading}
                variant="ghost"
                size="sm"
                className="gap-1"
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </PaginationItem>
          </PaginationContent>
        </Pagination>

        <div className="text-center mt-4 text-sm text-muted-foreground">
          Showing {Math.min((currentPage - 1) * eventsPerPage + 1, totalEvents)} to{' '}
          {Math.min(currentPage * eventsPerPage, totalEvents)} of {totalEvents} events
        </div>
      </div>
    );
  }
}
