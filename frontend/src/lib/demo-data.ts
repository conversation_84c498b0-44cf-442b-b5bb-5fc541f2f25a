
import { Channel, Event } from '@/interfaces/types';
import { addDays, subDays } from 'date-fns';

export const DEMO_CHANNELS: Channel[] = [
  {
    id: 'channel-1',
    name: 'Tech Today',
    description: 'The latest in technology news and product reviews, streaming live every week.',
    imageUrl: 'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?q=80&w=600',
    bannerUrl: 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?q=80&w=1200',
    socialLinks: 'twitter.com/techtoday, instagram.com/techtodaylive',
    status: 'approved',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    documents: {
      identityUrl: '',
      companyUrls: [],
      addressUrl: '',
      otherUrls: []
    }
  },
  {
    id: 'channel-2',
    name: 'Music Live',
    description: 'Live concerts and music sessions from emerging artists around the world.',
    imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=600',
    bannerUrl: 'https://images.unsplash.com/photo-1487958449943-2429e8be8625?q=80&w=1200',
    socialLinks: 'youtube.com/musiclive, facebook.com/musiclivechannel',
    status: 'approved',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    documents: {
      identityUrl: '',
      companyUrls: [],
      addressUrl: '',
      otherUrls: []
    }
  }
];

export const DEMO_EVENTS: Event[] = [
  {
    id: 'event-1',
    title: 'New Tech Showcase 2025',
    description: 'Join us for an exclusive preview of the most anticipated tech products of 2025.',
    imageUrl: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?q=80&w=600',
    price: 29.99,
    currency: 'USD',
    streamUrl: 'https://example.com/stream/event-1',
    startTime: addDays(new Date(), 7).toISOString(),
    endTime: addDays(new Date(), 7).toISOString(),
    status: 'published',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    channel: {
      id: 'channel-1',
      name: 'Tech Today',
      imageUrl: 'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?q=80&w=600'
    },
    thumbnail: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?q=80&w=600',
    date: addDays(new Date(), 7).toISOString(),
    address: 'Silicon Valley Conference Center, CA',
    channelId: 'channel-1',
    category: 'tech',
    pricingPlanId: 'premium',
    purchasedBy: ['user-1', 'user-2', 'user-3']
  },
  {
    id: 'event-2',
    title: 'Summer Music Festival',
    description: 'A full day of amazing performances from indie artists and major bands.',
    imageUrl: 'https://images.unsplash.com/photo-1501854140801-50d01698950b?q=80&w=600',
    price: 49.99,
    currency: 'USD',
    streamUrl: 'https://example.com/stream/event-2',
    startTime: addDays(new Date(), 15).toISOString(),
    endTime: addDays(new Date(), 15).toISOString(),
    status: 'published',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    channel: {
      id: 'channel-2',
      name: 'Music Live',
      imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=600'
    },
    thumbnail: 'https://images.unsplash.com/photo-1501854140801-50d01698950b?q=80&w=600',
    date: addDays(new Date(), 15).toISOString(),
    address: 'Central Park, New York',
    channelId: 'channel-2'
  },
  {
    id: 'event-3',
    title: 'Developer Conference 2025',
    description: 'Learn about the latest frameworks and tools from leading experts in software development.',
    imageUrl: 'https://images.unsplash.com/photo-1531297484001-80022131f5a1?q=80&w=600',
    price: 19.99,
    currency: 'USD',
    streamUrl: 'https://example.com/stream/event-3',
    startTime: subDays(new Date(), 10).toISOString(),
    endTime: subDays(new Date(), 10).toISOString(),
    status: 'published',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    channel: {
      id: 'channel-1',
      name: 'Tech Today',
      imageUrl: 'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?q=80&w=600'
    },
    thumbnail: 'https://images.unsplash.com/photo-1531297484001-80022131f5a1?q=80&w=600',
    date: subDays(new Date(), 10).toISOString(),
    address: 'Online Event',
    channelId: 'channel-1'
  }
];
