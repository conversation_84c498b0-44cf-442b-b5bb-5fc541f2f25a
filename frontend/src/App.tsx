import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AppProvider } from "./context/AppContext";
import { ThemeProvider } from "./components/theme/ThemeProvider";
import "./i18n/i18n";

// Import missing components
import { Header } from "./components/layout/Header";
import { Footer } from "./components/layout/Footer";
import NotFound from "./views/NotFound";

// Landing Page
import LandingPage from "./views/LandingPage";

// Auth Pages
import RegisterPage from "./views/auth/RegisterPage";
import RegisterSuccessPage from "./views/auth/RegisterSuccessPage";
import LoginPage from "./views/auth/LoginPage";
import MFAPage from "./views/auth/MFAPage";

// Main Pages
import HomePage from "./views/HomePage";
import CreateChannelPage from "./views/channels/CreateChannelPage";
import CreateEventPage from "./views/events/CreateEventPage";
import AdminPortalPage from "./views/admin/AdminPortalPage";
import ProfilePage from "./views/ProfilePage";
import EventDetailPage from "./views/events/EventDetailPage";
import ChannelViewPage from "./views/channels/ChannelViewPage";
import PricingPlansPage from "./views/admin/PricingPlansPage";
import CategoriesPage from "./views/admin/CategoriesPage";

// Admin Pages
import AdminUsersPage from "./views/admin/UsersPage";
import AdminChannelsPage from "./views/admin/ChannelsPage";
import AdminEventsPage from "./views/admin/EventsPage";

// Account Pages
import PasswordChangePage from "./views/account/security/PasswordChangePage";
import MFAManagementPage from "./views/account/security/MFAManagementPage";
import ChannelsPage from "./views/account/ChannelsPage";
import PurchasesPage from "./views/account/PurchasesPage";
import EditEventPage from "./views/events/EditEventPage";
import PaymentSettingsPage from "./views/account/PaymentSettingsPage";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider defaultTheme="system">
      <AppProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Header />
            <main className="min-h-[calc(100vh-8rem)] flex flex-col">
              <Routes>
                <Route path="/" element={<LandingPage />} />
                <Route path="/register" element={<RegisterPage />} />
                <Route path="/register-success" element={<RegisterSuccessPage />} />
                <Route path="/login" element={<LoginPage />} />
                <Route path="/mfa" element={<MFAPage />} />
                <Route path="/home" element={<HomePage />} />
                <Route path="/profile" element={<ProfilePage />} />
                <Route path="/channel-create" element={<CreateChannelPage />} />
                <Route path="/event-create" element={<CreateEventPage />} />
                
                {/* Admin Routes */}
                <Route path="/admin-portal" element={<AdminPortalPage />} />
                <Route path="/admin/pricing-plans" element={<PricingPlansPage />} />
                <Route path="/admin/categories" element={<CategoriesPage />} />
                <Route path="/admin/users" element={<AdminUsersPage />} />
                <Route path="/admin/channels" element={<AdminChannelsPage />} />
                <Route path="/admin/events" element={<AdminEventsPage />} />
                
                <Route path="/event/:id" element={<EventDetailPage />} />
                <Route path="/event-management/:id" element={<EventDetailPage contentCreatorView />} />
                <Route path="/channel/:id" element={<ChannelViewPage />} />
                <Route path="/channel/edit/:id" element={<ChannelViewPage />} />
                <Route path="/channel-edit/:id" element={<ChannelViewPage />} />
                
                {/* Account Security Routes */}
                <Route path="/account/security/password" element={<PasswordChangePage />} />
                <Route path="/account/security/mfa" element={<MFAManagementPage />} />
                
                {/* Account Management Routes */}
                <Route path="/account/channels" element={<ChannelsPage />} />
                <Route path="/account/purchases" element={<PurchasesPage />} />
                
                <Route path="/event/:id" element={<EventDetailPage />} />
                <Route path="/event/edit/:id" element={<EditEventPage />} />
                <Route path="/account/payment-settings" element={<PaymentSettingsPage />} />
                
                <Route path="*" element={<NotFound />} />
              </Routes>
            </main>
            <Footer />
          </BrowserRouter>
        </TooltipProvider>
      </AppProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
